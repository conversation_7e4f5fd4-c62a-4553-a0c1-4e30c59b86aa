# px-user-management

PX User Management is a secure user management component designed for handling user registration, authentication, and access control. It provides a set of RESTful APIs to manage user accounts, enforce security policies, and log key activities for auditing purposes.

## Key Capabilities
- **User Registration**: Register new users and securely store their credentials with hashed passwords.
- **User Authentication**:  Implement JWT-based authentication to verify users and manage access control.
- **User Management**: Update user details and account status. Provide administrators with user information, supporting search and pagination.
- **Password Management**: Email-base password management. Support password resets for forgotten passwords and allow users to change their passwords.
- **Account Verification**: Email-based account Verification. Verify new user email and update their account status.
- **Audit Logging**: Track key user actions in both the console and database for auditing purposes. Provide administrators with log information.

Access API document by visiting after start: `http://localhost:8081/swagger-ui/index.html`

## Prerequisites

- Docker Desktop
- Git
- OpenSSL

## Getting Started

1. Clone the repository
```bash
git clone <repository-url>
cd px-user-management
```

2. Generate private and public keys


```
openssl genpkey -algorithm RSA -out private.key -pkeyopt rsa_keygen_bits:2048
    
openssl rsa -pubout -in private.key -out public.key
```

3. Create a `.env` file in the root directory with the following variables:
```env
MYSQL_ROOT_PASSWORD=your_root_password
MYSQL_DATABASE=your_database_name
BACKEND_URL=http://localhost
PUBLIC_KEY_PATH=/spring-app/src/main/resources/public.key
PRIVATE_KEY_PATH=/spring-app/src/main/resources/private.key
FRONT_END_URL=http://localhost:8080/frontend

```

4. Start the application using Docker Compose
```bash
docker compose up -d
```

This will start:
- MySQL database on port 3307
- Spring Boot backend on port 8081
- Vue frontend on port 5173

5. Verify the application is running by visiting:
```
http://localhost:3307 - db - HeidiSQL for connection
http://localhost:8081 - backend
http://localhost:5173 - frontend
```

6. Access the API documentation by visiting:
```
http://localhost:8081/swagger-ui/index.html
```

## Stopping the Application

To stop all containers:
```bash
docker compose down
```

To stop and remove volumes:
```bash
docker compose down -v
```

## Attaching VSCode to the backend container

1. Install the official Docker extension.

![alt text](dockerExtension.png)

2. Click the `Attach Visual Studio Code` on the `openjdk` container.

![alt text](image.png)

> Note: Feel free to use any other IDE of you choice
