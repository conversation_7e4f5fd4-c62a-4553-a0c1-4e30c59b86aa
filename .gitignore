# database folder
database/

# env
.env

# Compiled class files
*.class

# Log files
*.log

# IntelliJ IDEA files
.idea/

# Eclipse files
.classpath
.project
.settings/

# Maven files
target/

# Gradle files
build/
.gradle/

# Spring Boot DevTools
.restart

# Spring Boot log file
spring.log

# IDE-specific files
.vscode/
*.iml
*.ipr
*.iws

# OS-specific files
.DS_Store
Thumbs.db

## from spring initializr gitignore 

HELP.md
.gradle
build/
!gradle/wrapper/gradle-wrapper.jar
!**/src/main/**/build/
!**/src/test/**/build/

### STS ###
.apt_generated
.classpath
.factorypath
.project
.settings
.springBeans
.sts4-cache
bin/
!**/src/main/**/bin/
!**/src/test/**/bin/

### IntelliJ IDEA ###
.idea
*.iws
*.iml
*.ipr
out/
!**/src/main/**/out/
!**/src/test/**/out/

### NetBeans ###
/nbproject/private/
/nbbuild/
/dist/
/nbdist/
/.nb-gradle/

### VS Code ###
.vscode/

### from vite ###

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

node_modules
.DS_Store
dist
dist-ssr
coverage
*.local

/cypress/videos/
/cypress/screenshots/

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

*.tsbuildinfo

*.key

# Project-specific
local-file-storage