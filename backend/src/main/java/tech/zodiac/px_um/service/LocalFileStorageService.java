package tech.zodiac.px_um.service;

import java.io.IOException;
import java.io.InputStream;
import java.net.MalformedURLException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Profile;
import org.springframework.core.io.Resource;
import org.springframework.core.io.UrlResource;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import tech.zodiac.px_um.config.LocalFileStorageProperties;
import tech.zodiac.px_um.exception.StorageException;

@Service
@Profile("dev")
public class LocalFileStorageService implements FileStorageService {
    private final Path rootLocation;
    
    @Autowired
    public LocalFileStorageService(LocalFileStorageProperties properties) {
        if (properties.getLocation().trim().length() == 0) {
            throw new StorageException("Local file upload location cannot be empty.");
        }
        this.rootLocation = Paths.get(properties.getLocation());
    }

    public void init() {
        try {
            Files.createDirectories(rootLocation);
        } catch (IOException e) {
            throw new StorageException("Failed to initialize storage.", e);
        }
    }

    @Override
    public String store(MultipartFile file) {
        try {
            if (file.isEmpty())
                throw new StorageException("File cannot be empty");

            Path destinationFile = this.rootLocation.resolve(
                Paths.get(file.getOriginalFilename())).normalize().toAbsolutePath();

            if (!destinationFile.getParent().equals(this.rootLocation.toAbsolutePath()))
                throw new StorageException("Cannot store file outside current directory");
            
            try (InputStream inputStream = file.getInputStream()) {
                Files.copy(inputStream, destinationFile, StandardCopyOption.REPLACE_EXISTING);
            }

            return destinationFile.toString();
        } catch (IOException e) {
            throw new StorageException("Failed to store file.", e);
        }
    }

    @Override
    public Resource load(String filename) {
        try {
            Path file = rootLocation.resolve(filename);
            Resource resource = new UrlResource(file.toUri());
            if (resource.exists() || resource.isReadable()) 
                return resource;
            else
                throw new StorageException("Failed to load file: " + filename);
        } catch (MalformedURLException e) {
            throw new StorageException("Could not read file: " + filename, e);
        }
    }
}
