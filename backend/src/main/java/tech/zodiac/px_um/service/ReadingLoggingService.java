package tech.zodiac.px_um.service;

import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import tech.zodiac.px_um.model.Reading;
import tech.zodiac.px_um.model.ReadingLog;
import tech.zodiac.px_um.model.User;
import tech.zodiac.px_um.repository.ReadingLogRepository;
import java.time.LocalDateTime;

@Slf4j
@Service
@RequiredArgsConstructor
public class ReadingLoggingService {

    private final ReadingLogRepository readingLogRepository;

    @Transactional
    public void logReadingCreation(User user, Reading reading) {
        ReadingLog readingLog = new ReadingLog(
                reading,
                user,
                "CREATED",
                LocalDateTime.now()
        );

        readingLogRepository.save(readingLog);

        log.info("READING LOG -- Action: {} User: {} Reading ID: {} At: {}",
                readingLog.getAction(),
                user.getId(),
                reading.getId(),
                readingLog.getTimestamp());
    }
}