package tech.zodiac.px_um.service;

import java.util.List;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import tech.zodiac.px_um.dto.ReadingDto;
import tech.zodiac.px_um.exception.ReadingNotFoundException;
import tech.zodiac.px_um.model.Reading;
import tech.zodiac.px_um.model.User;
import tech.zodiac.px_um.repository.ReadingRepository;
import tech.zodiac.px_um.utils.PaginationUtils;
import tech.zodiac.px_um.fhir.model.Observation;
import tech.zodiac.px_um.fhir.service.ReadingToObservationMapper;
import tech.zodiac.px_um.fhir.service.FhirObservationService;

@Service
public class ReadingService {
    @Autowired
    private ReadingRepository readingRepository;

    @Autowired
    private ReadingToObservationMapper observationMapper;

    @Autowired
    private FhirObservationService fhirObservationService;

    public Reading create(User user, ReadingDto reading) {
        // 1. Save reading locally first
        Reading readingEntity = new Reading(user, reading.getDetails());
        Reading savedReading = readingRepository.save(readingEntity);

        // 2. Convert to FHIR Observation and sync to Aidbox server
        try {
            Observation observation = observationMapper.readingToObservation(savedReading);
            ResponseEntity<String> fhirResponse = fhirObservationService.createObservationOnFhirServer(observation);

            // Log successful sync (you can add proper logging here)
            System.out.println("Reading " + savedReading.getId() + " synced to FHIR server. Status: " + fhirResponse.getStatusCode());

        } catch (Exception e) {
            // Log error but don't fail the reading creation
            System.err.println("Failed to sync reading " + savedReading.getId() + " to FHIR server: " + e.getMessage());
        }

        return savedReading;
    }

    public List<Reading> findAll() {
        return readingRepository.findAll();
    }

    public List<Reading> findByUser(User user, Optional<String> page) {
        Pageable pageable = PaginationUtils.getPageable(page);
        return readingRepository.findByUser(user, pageable);
    }

    public Reading getReadingById(Integer id) {
        return readingRepository.findById(id)
                .orElseThrow(() -> new ReadingNotFoundException("Reading not found"));
    }
}
