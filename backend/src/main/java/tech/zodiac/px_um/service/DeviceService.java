package tech.zodiac.px_um.service;

import java.util.List;
import java.util.Optional;

import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import tech.zodiac.px_um.dto.DeviceRegistrationDto;
import tech.zodiac.px_um.dto.UpdateDeviceDto;
import tech.zodiac.px_um.exception.DeviceNotFoundException;
import tech.zodiac.px_um.exception.ValidationException;
import tech.zodiac.px_um.model.Device;
import tech.zodiac.px_um.repository.DeviceRepository;

@Service
public class DeviceService {
    @Autowired
    private DeviceRepository deviceRepository;

    private final Integer DEVICES_PER_PAGE = 5;

    public Device register(DeviceRegistrationDto device) {
        Device deviceEntity = new Device(
            device.getName(), 
            device.getType(),
            device.getBrand(),
            device.getImageUrl());
        
        Device savedDevice = deviceRepository.save(deviceEntity);
        
        // TODO: Log the registration of a new device.
        return savedDevice;
    }

    public List<Device> findAll() {
        return deviceRepository.findAll();
    }

    public List<Device> findDeviceByCriteria(Optional<String> page, Optional<String> nameOptional) {
        Pageable pageable;

        if (page.isPresent()) {
            String pageString = page.get();
            if (pageString.equalsIgnoreCase(("all")))
                pageable = Pageable.unpaged();
            else {
                int pageNumber = Integer.parseInt(pageString);
                pageable = PageRequest.of(pageNumber, DEVICES_PER_PAGE);
            }
        } else {
            pageable = PageRequest.of(0, DEVICES_PER_PAGE);
        }

        String name = nameOptional.orElse(null);
        return deviceRepository.findByName(name, pageable);
    }

    public Device getDeviceById(Integer deviceId) {
        return deviceRepository.findById(deviceId).orElseThrow(() -> new DeviceNotFoundException("Device not found"));
    }

    public Device updatDevice(UpdateDeviceDto updateDeviceDto, Integer id) {
        StringBuilder detailMessage = new StringBuilder();

        Device storedDevice = deviceRepository.findById(id)
            .orElseThrow(() -> new DeviceNotFoundException("Device not found"));

        Optional.ofNullable(updateDeviceDto.getName()).ifPresent(name -> {
            if (name.isBlank())
                throw new ValidationException("Name cannot be blank");
            else {
                detailMessage.append("name updated from " + storedDevice.getName() + " to " + name);
                storedDevice.setName(name);
            }
        });

        Optional.ofNullable(updateDeviceDto.getType()).ifPresent(type -> {
            if (type.isBlank())
                throw new ValidationException("Type cannot be blank");
            else {
                detailMessage.append("type updated from " + storedDevice.getType() + " to " + type);
                storedDevice.setType(type);
            }
        });

        Optional.ofNullable(updateDeviceDto.getBrand()).ifPresent(brand -> {
            if (brand.isBlank())
                throw new ValidationException("Brand cannot be blank");
            else {
                detailMessage.append("brand updated from " + storedDevice.getBrand() + " to " + brand);
                storedDevice.setBrand(brand);
            }
        });

        Optional.ofNullable(updateDeviceDto.getImageUrl()).ifPresent(imageUrl -> {
            if (imageUrl.isBlank())
                throw new ValidationException("Image URL cannot be blank");
            else {
                detailMessage.append("imageUrl updated from " + storedDevice.getImageUrl() + " to " + imageUrl);
                storedDevice.setImageUrl(imageUrl);
            }
        });

        Device savedDevice = deviceRepository.save(storedDevice);
        
        return savedDevice;
    }
}
