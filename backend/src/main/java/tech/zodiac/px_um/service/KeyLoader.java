package tech.zodiac.px_um.service;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.nio.file.Files;
import java.nio.file.Paths;
import java.security.KeyFactory;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.Base64;

@Service
public class KeyLoader {

    private final String PUBLIC_KEY_PATH;
    private final String PRIVATE_KEY_PATH;

    public KeyLoader(@Value("${jwt.public-key-path}") String publicKeyPath,
                     @Value("${jwt.private-key-path}") String privateKeyPath) {
        PUBLIC_KEY_PATH = publicKeyPath;
        PRIVATE_KEY_PATH = privateKeyPath;
    }

    public PrivateKey loadPrivateKey() {

        try {
            String key = Files.readString(Paths.get(PRIVATE_KEY_PATH))
                    .replace("-----BEGIN PRIVATE KEY-----", "")
                    .replace("-----END PRIVATE KEY-----", "")
                    .replaceAll("\\s", "");

            byte[] keyBytes = Base64.getDecoder().decode(key);

            PKCS8EncodedKeySpec spec = new PKCS8EncodedKeySpec(keyBytes);
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            return keyFactory.generatePrivate(spec);

        } catch (Exception e) {
            throw new RuntimeException("Could not load private key", e);
        }
    }

    public PublicKey loadPublicKey() {
        try {
            String key = Files.readString(Paths.get(PUBLIC_KEY_PATH))
                    .replace("-----BEGIN PUBLIC KEY-----", "")
                    .replace("-----END PUBLIC KEY-----", "")
                    .replaceAll("\\s", "");

            byte[] keyBytes = Base64.getDecoder().decode(key);
            X509EncodedKeySpec spec = new X509EncodedKeySpec(keyBytes);
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            return keyFactory.generatePublic(spec);
        } catch (Exception e) {
            throw new RuntimeException("Could not load public key", e);
        }
    }
}
