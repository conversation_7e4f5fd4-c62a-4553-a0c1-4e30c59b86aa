package tech.zodiac.px_um.utils;

import java.util.Optional;

import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;

public class PaginationUtils {
    private static final int ITEMS_PER_PAGE = 5; // TODO: Make this Spring-aware

    private PaginationUtils() {
    }

    public static Pageable getPageable(Optional<String> page) {
        if (page.isPresent()) {
            String pageString = page.get();
            if (pageString.equalsIgnoreCase("all")) {
                return Pageable.unpaged();
            } else {
                int pageNumber = Integer.parseInt(pageString) - 1;
                return PageRequest.of(pageNumber, ITEMS_PER_PAGE);
            }
        } else {
            return PageRequest.of(0, ITEMS_PER_PAGE);
        }
    }
}
