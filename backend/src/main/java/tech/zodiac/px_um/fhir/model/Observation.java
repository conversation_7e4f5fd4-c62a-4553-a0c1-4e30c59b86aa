package tech.zodiac.px_um.fhir.model;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * Simple FHIR Observation model - only essential fields
 */
public class Observation {

    @JsonProperty("resourceType")
    private String resourceType = "Observation";

    private String status;
    private CodeableConcept code;
    private Quantity valueQuantity;
    private String effectiveDateTime;

    public Observation() {
    }

    public String getResourceType() {
        return resourceType;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public CodeableConcept getCode() {
        return code;
    }

    public void setCode(CodeableConcept code) {
        this.code = code;
    }



    public Quantity getValueQuantity() {
        return valueQuantity;
    }

    public void setValueQuantity(Quantity valueQuantity) {
        this.valueQuantity = valueQuantity;
    }

    public String getEffectiveDateTime() {
        return effectiveDateTime;
    }

    public void setEffectiveDateTime(String effectiveDateTime) {
        this.effectiveDateTime = effectiveDateTime;
    }
}
