package tech.zodiac.px_um.fhir.service;

import org.springframework.stereotype.Service;
import tech.zodiac.px_um.fhir.model.Observation;
import tech.zodiac.px_um.fhir.model.CodeableConcept;
import tech.zodiac.px_um.fhir.model.Quantity;
import tech.zodiac.px_um.model.Reading;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Map;

/**
 * Simple service to map Reading data to FHIR Observation
 */
@Service
public class ReadingToObservationMapper {

    /**
     * Convert a Reading entity to a FHIR Observation
     */
    public Observation readingToObservation(Reading reading) {
        if (reading == null) {
            return null;
        }

        Observation observation = new Observation();

        // Set basic FHIR fields
        observation.setStatus("final");

        // Use timestamp from device reading if available, otherwise current time
        String effectiveDateTime = extractTimestamp(reading.getDetails());
        observation.setEffectiveDateTime(effectiveDateTime);

        // Set observation code
        observation.setCode(new CodeableConcept("Health Reading"));

        // Set a simple value from the reading data
        setObservationValue(reading.getDetails(), observation);

        return observation;
    }

    /**
     * Set a value from reading data
     */
    private void setObservationValue(Map<String, Object> details, Observation observation) {
        if (details == null || details.isEmpty()) {
            return;
        }

        // Try to get systolic blood pressure as the main value
        if (details.containsKey("deviceReading")) {
            Map<String, Object> deviceReading = (Map<String, Object>) details.get("deviceReading");
            if (deviceReading.containsKey("systolic")) {
                Double systolic = ((Number) deviceReading.get("systolic")).doubleValue();
                Quantity quantity = new Quantity(systolic, "mmHg");
                observation.setValueQuantity(quantity);
                observation.setCode(new CodeableConcept("Systolic Blood Pressure"));
                return;
            }
        }

        // Fallback: just set a generic reading indicator
        Quantity quantity = new Quantity(1.0, "reading");
        observation.setValueQuantity(quantity);
    }

    /**
     * Extract timestamp from device reading or use current time
     */
    private String extractTimestamp(Map<String, Object> details) {
        if (details.containsKey("deviceReading")) {
            Map<String, Object> deviceReading = (Map<String, Object>) details.get("deviceReading");
            if (deviceReading.containsKey("timestamp")) {
                return deviceReading.get("timestamp").toString();
            }
        }
        return LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);
    }
}
