package tech.zodiac.px_um.fhir.service;

import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import tech.zodiac.px_um.fhir.model.Observation;

/**
 * Service to handle FHIR Observation operations with Aidbox server
 */
@Service
public class FhirObservationService {

//    private final String aidboxUrl = "http://************:8101/fhir/Patient";
//    private final String bearerToken = "Basic c3ByaW5nOnN1bW1lcg==";

    private final String observationUrl = "http://localhost:8101/fhir/Observation";
    private final String bearerToken = "Basic cm9vdDpoWnBCMkphbE50";

    /**
     * Send observation to Aidbox FHIR server
     */
    public ResponseEntity<String> createObservationOnFhirServer(Observation observation) {
        try {
            RestTemplate restTemplate = new RestTemplate();

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.parseMediaType("application/fhir+json"));
            headers.set("Authorization", bearerToken);

            HttpEntity<Observation> request = new HttpEntity<>(observation, headers);
            ResponseEntity<String> response = restTemplate.postForEntity(
                observationUrl,
                request,
                String.class
            );

            return response;
        } catch (Exception e) {
            throw new RuntimeException("Failed to create observation on FHIR server: " + e.getMessage(), e);
        }
    }
}
