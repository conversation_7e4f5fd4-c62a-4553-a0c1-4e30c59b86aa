package tech.zodiac.px_um.filter;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;
import tech.zodiac.px_um.config.JwtAuthenticationEntryPoint;
import tech.zodiac.px_um.exception.AuthException;
import tech.zodiac.px_um.exception.InvalidTokenException;
import tech.zodiac.px_um.exception.UserAccessDeniedException;
import tech.zodiac.px_um.exception.UserNotFoundException;
import tech.zodiac.px_um.model.User;
import tech.zodiac.px_um.service.JwtService;
import tech.zodiac.px_um.service.UserService;

import java.io.IOException;
import java.util.Set;

@Component
public class UserStatusFilter extends OncePerRequestFilter {
    private final JwtService jwtService;
    private final UserService userService;
    private final Set<String> excludedEndpoints;
    private final JwtAuthenticationEntryPoint jwtAuthenticationEntryPoint;

    public UserStatusFilter(JwtService jwtService, UserService userService, Set<String> excludedEndpoints, JwtAuthenticationEntryPoint jwtAuthenticationEntryPoint) {
        this.jwtService = jwtService;
        this.userService = userService;
        this.excludedEndpoints = excludedEndpoints;
        this.jwtAuthenticationEntryPoint = jwtAuthenticationEntryPoint;
    }

    @Override
    public void doFilterInternal(
            @NonNull HttpServletRequest request,
            @NonNull HttpServletResponse response,
            @NonNull FilterChain filterChain
    ) throws IOException, ServletException {

        // Bypass token validation for excluded endpoints
        if (excludedEndpoints.contains(request.getRequestURI())) {
            filterChain.doFilter(request, response);
            return;
        }


        try {
            String token = request.getHeader(HttpHeaders.AUTHORIZATION);
            if (token != null && token.startsWith("Bearer ")) {
                token = token.substring(7);

                Integer userId = jwtService.extractUserId(token);
                User user = userService.getUserById(userId);

                if (user == null) {
                    throw new UserNotFoundException("User not found");
                }

                if (!user.isActive()) {
                    jwtService.deleteRefreshTokenByUserId(userId);
                    throw new UserAccessDeniedException("User is not active");
                }

                request.setAttribute("user", user);
            }
            filterChain.doFilter(request, response);
        } catch (AuthException e) {
            request.setAttribute("SPRING_SECURITY_LAST_EXCEPTION", e);
            jwtAuthenticationEntryPoint.commence(request, response, e);
        }
    }
}
