package tech.zodiac.px_um.filter;

import io.jsonwebtoken.ExpiredJwtException;
import io.jsonwebtoken.JwtException;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.lang.NonNull;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;
import tech.zodiac.px_um.config.JwtAuthenticationEntryPoint;
import tech.zodiac.px_um.exception.AuthException;
import tech.zodiac.px_um.exception.InvalidTokenException;
import tech.zodiac.px_um.model.User;
import tech.zodiac.px_um.repository.UserRepository;
import tech.zodiac.px_um.service.JwtService;

import java.io.IOException;
import java.util.List;
import java.util.Set;

@Component
public class JwtAuthenticationFilter extends OncePerRequestFilter {

    private final JwtService jwtService;
    private final Set<String> excludedEndpoints;
    private final Set<String> refreshEndpoints;
    private final UserRepository userRepository;
    private final JwtAuthenticationEntryPoint jwtAuthenticationEntryPoint;

    public JwtAuthenticationFilter(JwtService jwtService,
                                   @Qualifier("excludedEndpoints") Set<String> excludedEndpoints,
                                   @Qualifier("refreshEndpoints") Set<String> refreshEndpoints, UserRepository userRepository, JwtAuthenticationEntryPoint jwtAuthenticationEntryPoint) {
        this.jwtService = jwtService;
        this.excludedEndpoints = excludedEndpoints;
        this.refreshEndpoints = refreshEndpoints;
        this.userRepository = userRepository;
        this.jwtAuthenticationEntryPoint = jwtAuthenticationEntryPoint;
    }

    @Override
    public void doFilterInternal(
            @NonNull HttpServletRequest request,
            @NonNull HttpServletResponse response,
            @NonNull FilterChain filterChain
    ) throws IOException, ServletException {

        String requestURI = request.getRequestURI();

        // Bypass token validation for excluded endpoints
        if (excludedEndpoints.contains(requestURI)) {
            filterChain.doFilter(request, response);
            return;
        }

        try {
            String token = request.getHeader(HttpHeaders.AUTHORIZATION);
            if (token != null && token.startsWith("Bearer ")) {
                token = token.substring(7);
                Integer userId = jwtService.extractUserId(token);

                if (refreshEndpoints.contains(requestURI)) {
                    if (jwtService.validateRefreshToken(token, userId)) {
                        setContextAuthentication(userId);
                    } else {
                        throw new InvalidTokenException("Invalid refresh token");
                    }
                } else {
                    if (jwtService.validateAccessToken(token, userId)) {
                        setContextAuthentication(userId);
                    } else {
                        throw new InvalidTokenException("Invalid access token");
                    }
                }
            }
            filterChain.doFilter(request, response);
        } catch (AuthException e) {
            request.setAttribute("SPRING_SECURITY_LAST_EXCEPTION", e);
            jwtAuthenticationEntryPoint.commence(request, response, e);
        }
    }

    private void setContextAuthentication(Integer userId) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new UsernameNotFoundException("User not found: " + userId));

        List<GrantedAuthority> authorities = List.of(new SimpleGrantedAuthority("ROLE_" + user.getRole()));

        UsernamePasswordAuthenticationToken authentication =
                new UsernamePasswordAuthenticationToken(userId, null, authorities);
        SecurityContextHolder.getContext().setAuthentication(authentication);
    }
}