package tech.zodiac.px_um.controller;

import java.util.List;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;
import tech.zodiac.px_um.dto.ReadingDto;
import tech.zodiac.px_um.model.Reading;
import tech.zodiac.px_um.model.User;
import tech.zodiac.px_um.service.ReadingService;
import tech.zodiac.px_um.service.UserService;
import tech.zodiac.px_um.service.ReadingLoggingService;

@RestController
@RequestMapping("/readings")
public class ReadingController {
    @Autowired
    private ReadingService readingService;

    @Autowired
    private UserService userService;

    @Autowired
    private ReadingLoggingService readingLoggingService;

    @GetMapping("/{id}")
    public ResponseEntity<Reading> getReadingById(@PathVariable Integer id) {
        return ResponseEntity.ok(readingService.getReadingById(id));
    }

    @GetMapping
    public ResponseEntity<List<Reading>> getReadings(Authentication authentication,
            @RequestParam Optional<String> page) {
        Integer userId = Integer.valueOf(authentication.getName());
        User user = userService.getUserById(userId);

        return ResponseEntity.ok(readingService.findByUser(user, page));
    }

    @PostMapping
    public ResponseEntity<Reading> createReading(Authentication authentication,
            @Valid @RequestBody ReadingDto reading) {
        Integer userId = Integer.valueOf(authentication.getName());
        User user = userService.getUserById(userId);

        Reading newReading = readingService.create(user, reading);

        readingLoggingService.logReadingCreation(user, newReading);

        return ResponseEntity.status(HttpStatus.CREATED).body(newReading);
    }
}