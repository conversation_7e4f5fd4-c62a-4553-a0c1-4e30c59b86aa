package tech.zodiac.px_um.controller;

import java.util.List;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;
import tech.zodiac.px_um.dto.DeviceRegistrationDto;
import tech.zodiac.px_um.dto.UpdateDeviceDto;
import tech.zodiac.px_um.model.Device;
import tech.zodiac.px_um.service.DeviceService;

@RestController
@RequestMapping("/devices")
public class DeviceController {
    @Autowired
    private DeviceService deviceService;

    @GetMapping
    public ResponseEntity<List<Device>> getDevices(@RequestParam Optional<String> page,
                                                   @RequestParam Optional<String> name) {
        return ResponseEntity.ok(deviceService.findDeviceByCriteria(page, name));
    }

    @GetMapping("/{id}")
    public ResponseEntity<Device> getDeviceById(@PathVariable Integer id) {
        return ResponseEntity.ok(deviceService.getDeviceById(id));
    }

    @PatchMapping("/{id}")
    public ResponseEntity<Device> updateDevice(@Valid @RequestBody UpdateDeviceDto updateDeviceDto, 
                                               @PathVariable Integer id) {
        Device updatedDevice = deviceService.updatDevice(updateDeviceDto, id);
        return ResponseEntity.ok(updatedDevice);

    }

    @PostMapping
    public ResponseEntity<Device> createDevice(@Valid @RequestBody DeviceRegistrationDto device) {
        Device newDevice = deviceService.register(device);
        return ResponseEntity.status(HttpStatus.CREATED).body(newDevice);
    }
}
