package tech.zodiac.px_um.repository;

import java.util.List;
import java.util.Optional;

import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import tech.zodiac.px_um.model.Device;

public interface DeviceRepository extends JpaRepository<Device, Integer> {
    Optional<Device> findById(Integer id);

    @Query("SELECT d FROM Device d " +
            "WHERE (:name IS NULL OR LOWER(d.name) LIKE LOWER(CONCAT('%', :name, '%')))")
    List<Device> findByName(String name, Pageable pageable);
}