package tech.zodiac.px_um.repository;

import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import tech.zodiac.px_um.model.Role;
import tech.zodiac.px_um.model.User;

import java.util.List;
import java.util.Optional;

public interface UserRepository extends JpaRepository<User, Integer> {
    Optional<User> findByEmail(String email);

    @Query("SELECT u FROM User u " +
            "WHERE ((:name IS NULL) OR (LOWER(CONCAT(u.firstName, ' ', u.lastName)) LIKE LOWER(CONCAT('%', :name, '%')))) "
            +
            "AND (:role IS NULL OR u.role = :role)")
    List<User> findAllByRoleAndName(Role role, String name, Pageable pageable);
}
