package tech.zodiac.px_um.model;

import java.util.Map;

import jakarta.persistence.*;
import tech.zodiac.px_um.converter.HashMapConverter;

@Entity
@Table(name = "`reading`")
public class Reading {
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private Integer id;

    @ManyToOne
    @JoinColumn(name = "user_id", nullable = false)
    private User user;

    @Column(columnDefinition = "json")
    @Convert(converter = HashMapConverter.class)
    private Map<String, Object> details;

    public Reading() {
    }

    public Reading(User user, Map<String, Object> details) {
        this.user = user;
        this.details = details;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    public Map<String, Object> getDetails() {
        return details;
    }

    public void setMetadata(Map<String, Object> details) {
        this.details = details;
    }
}
