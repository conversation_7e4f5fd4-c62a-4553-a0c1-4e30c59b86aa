package tech.zodiac.px_um.dto;

import jakarta.validation.constraints.Email;
import tech.zodiac.px_um.model.Gender;

import java.time.LocalDate;

public class UpdateUserDto {
    private String firstName;
    private String lastName;

    @Email(message = "Invalid email format")
    private String email;

    private LocalDate dob;

    private Gender gender;

    public UpdateUserDto() {
    }

    public UpdateUserDto(String firstName, String lastName, String email, LocalDate dob, Gender gender) {
        this.firstName = firstName;
        this.lastName = lastName;
        this.email = email;
        this.dob = dob;
        this.gender = gender;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public LocalDate getDob() {
        return dob;
    }

    public void setDob(LocalDate dob) {
        this.dob = dob;
    }

    public Gender getGender() {
        return gender;
    }

    public void setGender(Gender gender) {
        this.gender = gender;
    }
}
