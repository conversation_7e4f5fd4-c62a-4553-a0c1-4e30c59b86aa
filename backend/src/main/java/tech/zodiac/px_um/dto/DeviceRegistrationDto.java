package tech.zodiac.px_um.dto;

import jakarta.validation.constraints.NotBlank;

public class DeviceRegistrationDto {
    @NotBlank(message = "Name is required.")
    private String name;
    @NotBlank(message = "Type is required.")
    private String type;
    @NotBlank(message = "Brand is required.")
    private String brand;
    @NotBlank(message = "Image URL is required.")
    private String imageUrl;

    public DeviceRegistrationDto(String name, String type, String brand, String imageUrl) {
        this.name = name;
        this.type = type;
        this.brand = brand;
        this.imageUrl = imageUrl;
    }

    public DeviceRegistrationDto() {

    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
    
    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }
}
