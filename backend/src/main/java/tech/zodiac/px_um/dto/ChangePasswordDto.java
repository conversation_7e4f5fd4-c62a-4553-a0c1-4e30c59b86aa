package tech.zodiac.px_um.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

public class ChangePasswordDto {
    @NotBlank
    @Size(min = 8, message = "Password must have at least 8 characters")
    private String current_password;
    @NotBlank
    @Size(min = 8, message = "Password must have at least 8 characters")
    private String new_password;

    public ChangePasswordDto() {
    }

    public ChangePasswordDto(String current_password, String new_password) {
        this.current_password = current_password;
        this.new_password = new_password;
    }

    public @NotBlank @Size(min = 8, message = "Password must have at least 8 characters") String getCurrent_password() {
        return current_password;
    }

    public void setCurrent_password(@NotBlank @Size(min = 8, message = "Password must have at least 8 characters") String current_password) {
        this.current_password = current_password;
    }

    public @NotBlank @Size(min = 8, message = "Password must have at least 8 characters") String getNew_password() {
        return new_password;
    }

    public void setNew_password(@NotBlank @Size(min = 8, message = "Password must have at least 8 characters") String new_password) {
        this.new_password = new_password;
    }
}
