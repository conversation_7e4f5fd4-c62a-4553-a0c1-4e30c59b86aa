package tech.zodiac.px_um.dto;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;

public class ForgotPasswordDto {
    @NotBlank(message = "Email is required.")
    @Email(message = "Invalid email format.")
    private String email;

    public @NotBlank(message = "Email is required.") @Email(message = "Invalid email format.") String getEmail() {
        return email;
    }

    public void setEmail(@NotBlank(message = "Email is required.") @Email(message = "Invalid email format.") String email) {
        this.email = email;
    }
}
