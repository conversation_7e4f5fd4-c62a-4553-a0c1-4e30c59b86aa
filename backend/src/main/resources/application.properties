spring.application.name=px-um
spring.profiles.active=dev
jwt.private-key-path=${JWT_PRIVATE_KEY_PATH}
jwt.public-key-path=${JWT_PUBLIC_KEY_PATH}
front-end-url=${FRONT_END_URL}
springdoc.api-docs.path=/api-docs
springdoc.swagger-ui.path=/swagger-ui

### we haved it from the docker-compose.yml
# spring.datasource.url=
# spring.datasource.username=
# spring.datasource.password=
# spring.jpa.hibernate.ddl-auto=