package tech.zodiac.px_um.integration;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;
import tech.zodiac.px_um.dto.UserRegistrationDto;
import tech.zodiac.px_um.model.Gender;
import tech.zodiac.px_um.model.User;
import tech.zodiac.px_um.repository.EmailVerificationLogRepository;
import tech.zodiac.px_um.repository.UserActionLogRepository;
import tech.zodiac.px_um.repository.UserRepository;
import tech.zodiac.px_um.repository.VerificationTokenRepository;
import tech.zodiac.px_um.service.JwtService;
import tech.zodiac.px_um.service.UserService;

import java.time.LocalDate;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@SpringBootTest
@AutoConfigureMockMvc
@Transactional
class AccountVerificationIntegrationTest {

    @Autowired private MockMvc mockMvc;
    @Autowired private ObjectMapper objectMapper;
    @Autowired private UserService userService;
    @Autowired private UserRepository userRepository;
    @Autowired private VerificationTokenRepository verificationTokenRepository;
    @Autowired private JwtService jwtService;

    private final String testEmail = "<EMAIL>";
    private final String testPassword = "SecurePass123";
    private User registeredUser;

    @Autowired
    private EmailVerificationLogRepository emailVerificationLogRepository;

    @Autowired
    private UserActionLogRepository userActionLogRepository;

    @BeforeEach
    void setup() {
        verificationTokenRepository.deleteAll();
        emailVerificationLogRepository.deleteAll();
        userActionLogRepository.deleteAll();
        userRepository.deleteAll();

        UserRegistrationDto dto = new UserRegistrationDto("Verify", "User", testEmail, testPassword, LocalDate.of(2000, 1, 1), Gender.FEMALE);
        registeredUser = userService.register(dto);
    }

    @Test
    void generateVerificationLink_shouldReturn200WithURL() throws Exception {
        mockMvc.perform(post("/verify-link")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content("{\"email\":\"" + testEmail + "\"}"))
                .andExpect(status().isOk())
                .andExpect(content().string(org.hamcrest.Matchers.containsString("/verify-user?token=")));
    }

    @Test
    void generateVerificationLink_shouldReturn404ForUnknownEmail() throws Exception {
        mockMvc.perform(post("/verify-link")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content("{\"email\":\"<EMAIL>\"}"))
                .andExpect(status().isNotFound());
    }

    @Test
    void verifyUser_shouldMarkUserVerified_whenValidToken() throws Exception {
        // Get raw token from generateVerificationToken
        String rawToken = userService.register(
                new UserRegistrationDto("Jane", "Doe", "<EMAIL>", "pass123456", LocalDate.of(1995, 5, 10), Gender.FEMALE)
        ).getEmail();

        String verifyUrl = mockMvc.perform(post("/verify-link")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content("{\"email\":\"<EMAIL>\"}"))
                .andReturn()
                .getResponse()
                .getContentAsString();

        // Extract token from URL
        String token = verifyUrl.substring(verifyUrl.indexOf("token=") + 6);

        mockMvc.perform(post("/verify-user")
                        .header("Authorization", "Bearer " + token))
                .andExpect(status().isOk())
                .andExpect(content().string("User successfully verified"));

        Optional<User> user = userRepository.findByEmail("<EMAIL>");
        assertThat(user).isPresent();
        assertThat(user.get().isVerified()).isTrue();
    }

    @Test
    void verifyUser_shouldReturn400_whenInvalidToken() throws Exception {
        mockMvc.perform(post("/verify-user")
                        .header("Authorization", "Bearer invalid-token"))
                .andExpect(status().isUnauthorized())
                .andExpect(jsonPath("$.message").value("Token not found"));
    }

    @Test
    void resendVerifyLink_shouldReturn200_whenAuthenticated() throws Exception {
        String accessToken = jwtService.generateAccessToken(registeredUser.getId());

        mockMvc.perform(post("/resend-verify-link")
                        .header("Authorization", "Bearer " + accessToken))
                .andExpect(status().isOk())
                .andExpect(content().string(org.hamcrest.Matchers.containsString("/verify-user?token=")));
    }

    @Test
    void resendVerifyLink_shouldReturn401_whenNoToken() throws Exception {
        mockMvc.perform(post("/resend-verify-link"))
                .andExpect(status().isUnauthorized());
    }
}