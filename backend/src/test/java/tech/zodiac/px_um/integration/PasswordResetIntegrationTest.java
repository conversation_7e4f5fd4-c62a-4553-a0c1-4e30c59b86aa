package tech.zodiac.px_um.integration;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;
import tech.zodiac.px_um.dto.UserRegistrationDto;
import tech.zodiac.px_um.model.Gender;
import tech.zodiac.px_um.model.User;
import tech.zodiac.px_um.repository.*;
import tech.zodiac.px_um.service.UserService;

import java.time.LocalDate;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@SpringBootTest
@AutoConfigureMockMvc
@Transactional
class PasswordResetIntegrationTest {

    @Autowired private MockMvc mockMvc;
    @Autowired private ObjectMapper objectMapper;
    @Autowired private UserService userService;
    @Autowired private UserRepository userRepository;
    @Autowired private ResetPasswordTokenRepository resetPasswordTokenRepository;
    @Autowired private PasswordEncoder passwordEncoder;
    @Autowired private RefreshTokenRepository refreshTokenRepository;
    @Autowired private PasswordResetLogRepository passwordResetLogRepository;
    @Autowired private EmailVerificationLogRepository emailVerificationLogRepository;
    @Autowired private UserActionLogRepository userActionLogRepository;


    private final String email = "<EMAIL>";
    private final String password = "InitialPass123";
    private User testUser;

    @BeforeEach
    void setUp() {
        resetPasswordTokenRepository.deleteAll();
        refreshTokenRepository.deleteAll();
        emailVerificationLogRepository.deleteAll();
        passwordResetLogRepository.deleteAll();
        userActionLogRepository.deleteAll();

        userRepository.deleteAll();

        UserRegistrationDto dto = new UserRegistrationDto("Reset", "User", email, password, LocalDate.of(1990, 2, 2), Gender.OTHER);
        testUser = userService.register(dto);
    }

    @Test
    void forgotPassword_shouldReturnResetLink() throws Exception {
        mockMvc.perform(post("/forgot-password")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content("{\"email\":\"" + email + "\"}"))
                .andExpect(status().isOk())
                .andExpect(content().string(org.hamcrest.Matchers.containsString("/reset-password?token=")));
    }

    @Test
    void forgotPassword_shouldReturn404ForUnknownEmail() throws Exception {
        mockMvc.perform(post("/forgot-password")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content("{\"email\":\"<EMAIL>\"}"))
                .andExpect(status().isNotFound());
    }

    @Test
    void resetPassword_shouldUpdatePassword_whenValidToken() throws Exception {
        // Request reset link
        String url = mockMvc.perform(post("/forgot-password")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content("{\"email\":\"" + email + "\"}"))
                .andReturn()
                .getResponse()
                .getContentAsString();

        String token = url.substring(url.indexOf("token=") + 6);

        // Call /reset-password endpoint
        String newPassword = "NewSecurePassword123";

        mockMvc.perform(post("/reset-password")
                        .header("Authorization", "Bearer " + token)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content("{\"newPassword\": \"" + newPassword + "\"}"))
                .andExpect(status().isOk())
                .andExpect(content().string("Password successfully reset"));

        // Assert password is updated
        Optional<User> userOptional = userRepository.findByEmail(email);
        assertThat(userOptional).isPresent();

        User updatedUser = userOptional.get();
        assertTrue(passwordEncoder.matches(newPassword, updatedUser.getPassword()));
    }

    @Test
    void resetPassword_shouldReturn401_whenInvalidToken() throws Exception {
        mockMvc.perform(post("/reset-password")
                        .header("Authorization", "Bearer invalid-token")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content("{\"newPassword\":\"NewPass1234\"}"))
                .andExpect(status().isUnauthorized())
                .andExpect(jsonPath("$.message").value("Invalid token"));
    }

    @Test
    void resetPassword_shouldReturn400_whenNewPasswordTooShort() throws Exception {
        // Generate a valid token first
        String url = mockMvc.perform(post("/forgot-password")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content("{\"email\":\"" + email + "\"}"))
                .andReturn()
                .getResponse()
                .getContentAsString();

        String token = url.substring(url.indexOf("token=") + 6);

        // Send too-short password
        mockMvc.perform(post("/reset-password")
                        .header("Authorization", "Bearer " + token)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content("{\"newPassword\": \"short\"}"))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.message").value("Validation Failed: newPassword - Password must have at least 8 characters"));

    }
}