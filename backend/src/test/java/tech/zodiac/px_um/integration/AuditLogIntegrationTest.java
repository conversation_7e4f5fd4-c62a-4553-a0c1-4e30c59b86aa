package tech.zodiac.px_um.integration;

import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;
import tech.zodiac.px_um.dto.UserRegistrationDto;
import tech.zodiac.px_um.model.Gender;
import tech.zodiac.px_um.model.Role;
import tech.zodiac.px_um.model.User;
import tech.zodiac.px_um.repository.RefreshTokenRepository;
import tech.zodiac.px_um.repository.UserActionLogRepository;
import tech.zodiac.px_um.repository.UserRepository;
import tech.zodiac.px_um.service.UserService;

import java.time.LocalDate;
import java.util.Map;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@SpringBootTest
@AutoConfigureMockMvc
@Transactional
class AuditLogIntegrationTest {

    @Autowired private MockMvc mockMvc;
    @Autowired private ObjectMapper objectMapper;
    @Autowired private UserRepository userRepository;
    @Autowired private RefreshTokenRepository refreshTokenRepository;
    @Autowired private UserActionLogRepository userActionLogRepository;
    @Autowired private UserService userService;
    @Autowired private PasswordEncoder passwordEncoder;

    private User adminUser;
    private String accessToken;
    private final String email = "<EMAIL>";
    private final String rawPassword = "adminpass";

    @BeforeEach
    void setUp() {
        userActionLogRepository.deleteAll();
        refreshTokenRepository.deleteAll();
        userRepository.deleteAll();

        // Register user
        adminUser = userService.register(new UserRegistrationDto("Admin", "User", email, rawPassword, LocalDate.of(1980, 1, 1), Gender.MALE));

        // Ensure admin properties
        adminUser.setRole(Role.ADMIN);
        adminUser.setVerified(true);
        adminUser.setActive(true);
        userRepository.save(adminUser);

        // Mock request/response to simulate login
        HttpServletRequest mockRequest = Mockito.mock(HttpServletRequest.class);
        HttpServletResponse mockResponse = Mockito.mock(HttpServletResponse.class);
        Mockito.when(mockRequest.getRemoteAddr()).thenReturn("127.0.0.1");

        Map<String, String> tokens = userService.login(email, rawPassword, mockRequest, mockResponse);
        accessToken = tokens.get("access_token");
    }

    @Test
    void shouldLogUserLogin_andAllowAdminToFetchLogs() throws Exception {
        mockMvc.perform(get("/logs/users")
                        .param("type", "USER_ACTION")
                        .param("userId", String.valueOf(adminUser.getId()))
                        .header("Authorization", "Bearer " + accessToken))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$").isArray());
    }
}