package tech.zodiac.px_um.integration;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

import tech.zodiac.px_um.model.Gender;
import tech.zodiac.px_um.model.User;
import tech.zodiac.px_um.repository.RefreshTokenRepository;
import tech.zodiac.px_um.repository.UserRepository;

import java.time.LocalDate;
import java.util.Map;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@SpringBootTest
@AutoConfigureMockMvc
@Transactional
public class UserLoginIntegrationTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Autowired
    private RefreshTokenRepository refreshTokenRepository;

    private static final String VALID_EMAIL = "<EMAIL>";
    private static final String VALID_PASSWORD = "SecurePassword123";

    @BeforeEach
    public void setUp() {
        refreshTokenRepository.deleteAll();
        userRepository.deleteAll();

        User user = new User("John", "Doe", VALID_EMAIL, passwordEncoder.encode(VALID_PASSWORD), LocalDate.of(1990, 6, 15), Gender.MALE);
        user.setActive(true);
        user.setVerified(true);

        userRepository.save(user);
    }

    @Test
    public void login_shouldReturn200AndTokens_forValidCredentials() throws Exception {
        Map<String, String> request = Map.of(
                "email", VALID_EMAIL,
                "password", VALID_PASSWORD
        );

        mockMvc.perform(post("/users/login")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.access_token").exists())
                .andExpect(jsonPath("$.refresh_token").exists());
    }

    @Test
    public void login_shouldReturn401_forInvalidPassword() throws Exception {
        Map<String, String> request = Map.of(
                "email", VALID_EMAIL,
                "password", "WrongPassword123"
        );

        mockMvc.perform(post("/users/login")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isUnauthorized())
                .andExpect(jsonPath("$.message").value("Invalid Password"));
    }

    @Test
    public void login_shouldReturn401_forInvalidEmail() throws Exception {
        Map<String, String> request = Map.of(
                "email", "<EMAIL>",
                "password", "whateverPassword"
        );

        mockMvc.perform(post("/users/login")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isNotFound())
                .andExpect(jsonPath("$.message").value("User Not Found"));
    }

    @Test
    public void login_shouldReturn403_forDeactivatedUser() throws Exception {
        // Deactivate user
        User user = userRepository.findByEmail(VALID_EMAIL).get();
        user.setActive(false);
        userRepository.save(user);

        Map<String, String> request = Map.of(
                "email", VALID_EMAIL,
                "password", VALID_PASSWORD
        );

        mockMvc.perform(post("/users/login")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isForbidden())
                .andExpect(jsonPath("$.message").value("User is Deactivated"));
    }

    @Test
    public void login_shouldReturn400_forInvalidInput() throws Exception {
        Map<String, String> request = Map.of(
                "email", "invalid-email",
                "password", "123"
        );

        mockMvc.perform(post("/users/login")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.message").exists())
                .andExpect(jsonPath("$.message").value(org.hamcrest.Matchers.containsString("Invalid email format")));
    }
}