package tech.zodiac.px_um.integration;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.hamcrest.Matchers;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

import tech.zodiac.px_um.model.Gender;
import tech.zodiac.px_um.model.User;
import tech.zodiac.px_um.repository.UserActionLogRepository;
import tech.zodiac.px_um.repository.UserRepository;

import java.time.LocalDate;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@SpringBootTest
@AutoConfigureMockMvc
@Transactional
public class UserRegistrationIntegrationTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private UserActionLogRepository userActionLogRepository;

    @BeforeEach
    public void setUp() {
        // Clear logs and users to avoid foreign key issues
        userActionLogRepository.deleteAll();
        userRepository.deleteAll();
    }

    @Test
    public void registerUser_shouldReturn201Created() throws Exception {
        String requestBody = """
            {
                "firstName": "John",
                "lastName": "Doe",
                "email": "<EMAIL>",
                "password": "securePassword123",
                "dob": "1990-01-01",
                "gender": "MALE"
            }
        """;

        mockMvc.perform(post("/users/registration")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(requestBody))
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.id").exists())
                .andExpect(jsonPath("$.email").value("<EMAIL>"))
                .andExpect(jsonPath("$.firstName").value("John"))
                .andExpect(jsonPath("$.lastName").value("Doe"))
                .andExpect(jsonPath("$.role").value("USER"))
                .andExpect(jsonPath("$.active").value(true));
    }

    @Test
    public void registerUser_shouldReturn400ForDuplicateEmail() throws Exception {
        // Seed an existing user
        User existing = new User(
                "John",
                "Doe",
                "<EMAIL>",
                "hashedPassword",
                LocalDate.of(1990, 1, 1),
                Gender.MALE
        );
        userRepository.save(existing);

        String requestBody = """
            {
                "firstName": "Another",
                "lastName": "User",
                "email": "<EMAIL>",
                "password": "securePassword123",
                "dob": "1995-12-12",
                "gender": "FEMALE"
            }
        """;

        mockMvc.perform(post("/users/registration")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(requestBody))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.message").value(Matchers.containsString("already exists")));
    }

    @Test
    public void registerUser_shouldReturn400ForInvalidInput() throws Exception {
        String requestBody = """
            {
                "firstName": "",
                "lastName": "",
                "email": "invalid-email",
                "password": "123",
                "dob": "",
                "gender": ""
            }
        """;

        mockMvc.perform(post("/users/registration")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(requestBody))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.message", Matchers.containsString("firstName - First name is required.")))
                .andExpect(jsonPath("$.message", Matchers.containsString("lastName - Last name is required.")))
                .andExpect(jsonPath("$.message", Matchers.containsString("email - Invalid email format.")))
                .andExpect(jsonPath("$.message", Matchers.containsString("password - Password must have at least 8 characters")))
                .andExpect(jsonPath("$.message", Matchers.containsString("dob - Date of birth is required.")))
                .andExpect(jsonPath("$.message", Matchers.containsString("gender - Gender is required.")));
    }
}
