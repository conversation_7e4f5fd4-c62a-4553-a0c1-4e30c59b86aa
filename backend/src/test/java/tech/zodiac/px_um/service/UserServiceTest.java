package tech.zodiac.px_um.service;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.PageRequest;
import org.springframework.security.crypto.password.PasswordEncoder;
import tech.zodiac.px_um.dto.UpdateUserDto;
import tech.zodiac.px_um.dto.UserRegistrationDto;
import tech.zodiac.px_um.exception.*;
import tech.zodiac.px_um.model.*;
import tech.zodiac.px_um.repository.UserRepository;

import java.time.LocalDate;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class UserServiceTest {
    @Mock
    private UserRepository userRepository;

    @Mock
    private PasswordEncoder passwordEncoder;

    @Mock
    private JwtService jwtService;

    @Mock
    private UserActionLogService userActionLogService;

    @Mock
    private PasswordResetLogService passwordResetLogService;

    @InjectMocks
    private UserService userService;

    private User testUser;

    @BeforeEach
    void setUp() {
        testUser = new User(
                "Test",
                "User",
                "<EMAIL>",
                "encodedPassword",
                LocalDate.of(2000, 1, 1),
                Gender.MALE
        );
        testUser.setId(1);
        testUser.setActive(true);
    }

    @Test
    void register_NewUser_Success() {
        UserRegistrationDto dto = new UserRegistrationDto(
                "New",
                "User",
                "<EMAIL>",
                "password",
                LocalDate.of(1995, 5, 10),
                Gender.FEMALE
        );
        when(userRepository.findByEmail(dto.getEmail())).thenReturn(Optional.empty());
        when(passwordEncoder.encode(dto.getPassword())).thenReturn("encodedPassword");
        when(userRepository.save(any(User.class))).thenReturn(testUser);

        User savedUser = userService.register(dto);

        assertNotNull(savedUser);
        assertEquals("Test", savedUser.getFirstName());
        assertEquals("User", savedUser.getLastName());
        verify(userRepository).save(any(User.class));
    }

    @Test
    void register_UserAlreadyExists_ThrowsException() {
        UserRegistrationDto dto = new UserRegistrationDto(
                "New",
                "User",
                "<EMAIL>",
                "password",
                LocalDate.of(1995, 5, 10),
                Gender.FEMALE
        );
        when(userRepository.findByEmail(dto.getEmail())).thenReturn(Optional.of(testUser));

        assertThrows(UserAlreadyExistsException.class, () -> userService.register(dto));
    }

    @Test
    void login_ValidCredentials_Success() {
        HttpServletRequest request = mock(HttpServletRequest.class);
        HttpServletResponse response = mock(HttpServletResponse.class);
        when(request.getRemoteAddr()).thenReturn("127.0.0.1");
        when(userRepository.findByEmail(testUser.getEmail())).thenReturn(Optional.of(testUser));
        when(passwordEncoder.matches("password", testUser.getPassword())).thenReturn(true);
        when(jwtService.generateAccessToken(anyInt())).thenReturn("accessToken");
        when(jwtService.generateRefreshToken(anyInt())).thenReturn("refreshToken");

        Map<String, String> tokens = userService.login("<EMAIL>", "password", request, response);

        assertNotNull(tokens);
        assertEquals("accessToken", tokens.get("access_token"));
    }

    @Test
    void login_InvalidPassword_ThrowsException() {
        HttpServletRequest request = mock(HttpServletRequest.class);
        when(userRepository.findByEmail(testUser.getEmail())).thenReturn(Optional.of(testUser));
        when(passwordEncoder.matches("wrongPassword", testUser.getPassword())).thenReturn(false);

        assertThrows(InvalidCredentialsException.class, () -> userService.login("<EMAIL>", "wrongPassword", request, mock(HttpServletResponse.class)));
    }
    @Test
    void findUsersByCriteria_ValidCriteria_Success() {
        Optional<String> page = Optional.of("1");
        Optional<String> roleOptional = Optional.of("USER");
        Optional<String> nameOptional = Optional.of("Test User");

        Role role = Role.valueOf(roleOptional.get());
        String name = nameOptional.get();

        List<User> userList = Collections.singletonList(testUser);
        when(userRepository.findAllByRoleAndName(role, name, PageRequest.of(0,5))).thenReturn(userList);

        assertEquals(userList, userService.findUsersByCriteria(page, roleOptional, nameOptional));
        verify(userRepository,times(1)).findAllByRoleAndName(role, name, PageRequest.of(0,5));
    }

    @Test
    void getUserById_ValidId_Success() {
        Integer id = 1;
        when(userRepository.findById(id)).thenReturn(Optional.of(testUser));
        User savedUser = userService.getUserById(id);
        assertEquals(testUser, savedUser);
    }
    @Test
    void getUserById_InvalidId_ThrowsException() {
        Integer id = 2;
        when(userRepository.findById(id)).thenReturn(Optional.empty());
        UserNotFoundException thrown = assertThrows(UserNotFoundException.class, () -> userService.getUserById(id));
        assertEquals("User not found", thrown.getMessage());
        verify(userRepository,times(1)).findById(id);
    }

    @Test
    void updateUser_ValidUpdate_Success() {
        UpdateUserDto dto = new UpdateUserDto();
        dto.setFirstName("Updated");
        dto.setLastName("Name");

        when(userRepository.findById(1)).thenReturn(Optional.of(testUser));
        when(userRepository.save(any(User.class))).thenReturn(testUser);

        User updatedUser = userService.updateUser(dto, 1, 1);

        assertNotNull(updatedUser);
        assertEquals("Updated", updatedUser.getFirstName());
        assertEquals("Name", updatedUser.getLastName());
    }

    @Test
    void updateUser_ForbiddenUpdate_ThrowsException() {
        UpdateUserDto dto = new UpdateUserDto();
        dto.setFirstName("Updated");
        dto.setLastName("Name");

        when(userRepository.findById(2)).thenReturn(Optional.of(testUser));

        assertThrows(UserAccessDeniedException.class, () -> userService.updateUser(dto, 3, 2));
    }

    @Test
    void refreshAccessToken_ValidToken_Success() {
        when(jwtService.extractUserId("validToken")).thenReturn(1);
        when(jwtService.validateRefreshToken("validToken", 1)).thenReturn(true);
        when(userRepository.findById(1)).thenReturn(Optional.of(testUser));
        when(jwtService.generateAccessToken(1)).thenReturn("newAccessToken");

        Map<String, String> tokens = userService.refreshAccessToken("validToken");

        assertEquals("newAccessToken", tokens.get("access_token"));
    }

    @Test
    void refreshAccessToken_InvalidToken_ThrowsException() {
        String refreshToken = "invalidToken";
        int userId = 1;

        when(jwtService.extractUserId(refreshToken)).thenReturn(userId);
        when(jwtService.validateRefreshToken(eq(refreshToken), anyInt())).thenReturn(false);

        assertThrows(InvalidTokenException.class, () -> userService.refreshAccessToken(refreshToken));

        verify(jwtService).extractUserId(refreshToken);
        verify(jwtService).validateRefreshToken(refreshToken, userId);
        verifyNoMoreInteractions(userRepository, jwtService);
    }


    @Test
    void deactivateUserById_Admin_Success() {
        User admin = new User(
                "Admin",
                "User",
                "<EMAIL>",
                "password",
                LocalDate.of(1980, 2, 2),
                Gender.MALE
        );
        admin.setId(2);
        admin.setRole(Role.ADMIN);
        when(userRepository.findById(2)).thenReturn(Optional.of(admin));
        when(userRepository.findById(1)).thenReturn(Optional.of(testUser));
        when(userRepository.save(any(User.class))).thenReturn(testUser);

        userService.deactivateUserById(1, 2);

        verify(userRepository).save(any(User.class));
    }

    @Test
    void deactivateUserById_Forbidden_ThrowsException() {
        User user = new User(
                "User",
                "User",
                "<EMAIL>",
                "password",
                LocalDate.of(1991, 3, 3),
                Gender.OTHER
        );
        user.setId(2);
        user.setRole(Role.USER);
        when(userRepository.findById(2)).thenReturn(Optional.of(user));
        when(userRepository.findById(1)).thenReturn(Optional.of(testUser));
        assertThrows(UserAccessDeniedException.class, () -> userService.deactivateUserById(1, 2));
    }

    @Test
    void activateUserById_Admin_Success() {
        User admin = new User(
                "Admin",
                "User",
                "<EMAIL>",
                "password",
                LocalDate.of(1980, 2, 2),
                Gender.MALE
        );
        testUser.setActive(false);
        admin.setId(2);
        admin.setRole(Role.ADMIN);
        when(userRepository.findById(2)).thenReturn(Optional.of(admin));
        when(userRepository.findById(1)).thenReturn(Optional.of(testUser));
        when(userRepository.save(any(User.class))).thenReturn(testUser);
        userService.activateUserById(1, 2);

        verify(userRepository).save(any(User.class));
    }
    @Test
    void activateUserById_Forbidden_ThrowsException() {
        User user = new User(
                "User",
                "User",
                "<EMAIL>",
                "password",
                LocalDate.of(1991, 3, 3),
                Gender.OTHER
        );
        user.setId(2);
        user.setRole(Role.USER);
        when(userRepository.findById(1)).thenReturn(Optional.of(testUser
        ));
        when(userRepository.findById(2)).thenReturn(Optional.of(user));
        assertThrows(UserAccessDeniedException.class, () -> userService.activateUserById(1, 2));
    }

    @Test
    void changePassword_ValidChange_Success() {
        when(userRepository.findById(1)).thenReturn(Optional.of(testUser));
        when(passwordEncoder.matches("currentPassword", testUser.getPassword())).thenReturn(true);
        when(passwordEncoder.encode("newPassword")).thenReturn("encodedNewPassword");

        userService.changePassword(1, "currentPassword", "newPassword");

        verify(userRepository).save(any(User.class));
        verify(passwordResetLogService).addPasswordResetLog(any(User.class), any(PasswordEventType.class));
    }
    @Test
    void isValidPassword_Valid_Success() {
        String password = "newPassword";
        assertTrue (userService.isValidPassword(password));
    }

}
