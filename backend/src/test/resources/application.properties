# In-memory H2 DB for integration testing
spring.datasource.url=jdbc:h2:mem:testdb
spring.datasource.driverClassName=org.h2.Driver
spring.datasource.username=sa
spring.datasource.password=

# JPA settings
spring.jpa.database-platform=org.hibernate.dialect.H2Dialect
spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true

# Disable mail sending (if present)
spring.mail.host=localhost

# Required for KeyLoader and JwtService in tests
jwt.public-key-path=src/main/resources/public.key
jwt.private-key-path=src/main/resources/private.key

# Dummy value just for tests
front-end-url=http://localhost:4200
