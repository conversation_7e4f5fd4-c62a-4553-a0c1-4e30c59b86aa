import React, {
   createContext,
   useContext,
   useState,
   useEffect,
   ReactNode,
} from "react";

import { storage } from "@/utils/storage";
import { apiService } from "@/service/api";
import { LoginResponse, BackendUser } from "@/types/auth";
import { setTokenExpiry<PERSON><PERSON><PERSON> } from "@/service/api";
import { refreshToken } from "@/service/auth/refreshToken";

interface AuthState {
   accessToken: string | null;
   refreshToken: string | null;
   user: BackendUser | null;
   isAuthenticated: boolean;
   isLoading: boolean;
}

interface AuthContextType extends AuthState {
   signIn: (data: LoginResponse) => Promise<void>;
   processSignupSuccess: (userData: BackendUser) => void;
   signOut: () => Promise<void>;
   getAccessToken: () => Promise<string | null>;
   updateUser: (user: BackendUser) => void;
}

const ACCESS_TOKEN_KEY = "accessToken";
const REFRESH_TOKEN_KEY = "refreshToken";
const USER_INFO_KEY = "userInfo";

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
   children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
   const [authState, setAuthState] = useState<AuthState>({
      accessToken: null,
      refreshToken: null,
      user: null,
      isAuthenticated: false,
      isLoading: true,
   });

   useEffect(() => {
      const loadAuthData = async () => {
         try {
            const accessToken = await storage.getItem(ACCESS_TOKEN_KEY);
            const refreshToken = await storage.getItem(REFRESH_TOKEN_KEY);
            const userInfoStr = await storage.getItem(USER_INFO_KEY);

            let user: BackendUser | null = null;

            if (userInfoStr) {
               try {
                  user = JSON.parse(userInfoStr);
               } catch (e) {
                  console.error("Failed to parse user info from storage", e);
               }
            }

            if (accessToken && refreshToken) {
               setAuthState({
                  accessToken,
                  refreshToken,
                  user,
                  isAuthenticated: true,
                  isLoading: false,
               });
               console.log("Auth tokens loaded from storage.");
            } else {
               setAuthState((prev) => ({
                  ...prev,
                  user: null,
                  isLoading: false,
               }));
            }
         } catch (e) {
            console.error("Failed to load auth data from storage", e);
            setAuthState((prev) => ({ ...prev, user: null, isLoading: false }));
         }
      };

      loadAuthData();
      setTokenExpiryHandler(refreshToken);
      console.log("Token-expiry handler registered");
   }, []);

   const signIn = async (data: LoginResponse) => {
      try {
         // TODO: Why is this written twice??? See line 116
         await storage.setItem(ACCESS_TOKEN_KEY, String(data.access_token));
         await storage.setItem(REFRESH_TOKEN_KEY, String(data.refresh_token));

         const user: BackendUser = await apiService.get<BackendUser>(
            "/users/me",
            data.access_token,
         );

         await storage.setItem(USER_INFO_KEY, JSON.stringify(user));

         setAuthState({
            accessToken: data.access_token,
            refreshToken: data.refresh_token,
            user: user,
            isAuthenticated: true,
            isLoading: false,
         });

         // TODO: Why is this written twice??? See line 97
         await storage.setItem(ACCESS_TOKEN_KEY, String(data.access_token));
         await storage.setItem(REFRESH_TOKEN_KEY, String(data.refresh_token));

         console.log("Access, refresh tokens, and user info saved to storage.");
      } catch (e) {
         console.error("Failed to fetch or store user info", e);
      }
   };

   // TODO: What is the point of this function??
   const processSignupSuccess = async (userData: BackendUser) => {
      try {
         console.log("User registered, basic info stored:", userData);
      } catch (e) {
         console.error("Failed to process signup data", e);
      }
   };

   const signOut = async () => {
      const currentRefreshToken = authState.refreshToken;

      setAuthState({
         accessToken: null,
         refreshToken: null,
         user: null,
         isAuthenticated: false,
         isLoading: false,
      });

      try {
         await storage.removeItem(ACCESS_TOKEN_KEY);
         await storage.removeItem(REFRESH_TOKEN_KEY);
         await storage.removeItem(USER_INFO_KEY);
         console.log("Auth tokens deleted from storage.");

         if (currentRefreshToken) {
            try {
               await apiService.post<string>(
                  "/users/logout",
                  {},
                  currentRefreshToken,
               );
               console.log("Successfully logged out from backend.");
            } catch (e) {
               console.error(
                  "Failed to logout from backend, but cleared local session.",
                  e,
               );
            }
         }
      } catch (e) {
         console.error("Failed to delete auth data", e);
      }
   };

   const getAccessToken = async (): Promise<string | null> => {
      if (authState.accessToken) {
         return authState.accessToken;
      }
      return null;
   };

   const updateUser = (user: BackendUser) => {
      setAuthState((prev) => ({
         ...prev,
         user,
      }));
   };

   const value = {
      ...authState,
      signIn,
      processSignupSuccess,
      signOut,
      getAccessToken,
      updateUser,
   };

   return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

export const useAuth = (): AuthContextType => {
   const context = useContext(AuthContext);
   if (context === undefined) {
      throw new Error("useAuth must be used within an AuthProvider");
   }
   return context;
};
