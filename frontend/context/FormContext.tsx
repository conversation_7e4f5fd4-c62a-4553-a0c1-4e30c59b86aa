import React, { createContext, ReactNode, useContext, useState } from "react";

interface FormData {
   [key: string]: string;
}

interface FormContextType {
   formData: FormData;
   setFormData: React.Dispatch<React.SetStateAction<{}>>;
}

const FormContext = createContext<FormContextType | undefined>(undefined);

export const FormProvider: React.FC<{ children: ReactNode }> = ({
   children,
}) => {
   const [formData, setFormData] = useState({});
   const value = { formData, setFormData };

   return <FormContext.Provider value={value}>{children}</FormContext.Provider>;
};

export const useFormContext = () => {
   const context = useContext(FormContext);
   if (context === undefined)
      throw new Error("useFormContext must be used within a FormProvider");

   return context;
};
