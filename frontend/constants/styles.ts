import { Dimensions, Platform, StyleSheet } from "react-native";

import { ThemeColors } from "@/context/ThemeContext";
import { spacing } from "./spacing";
import { Colors } from "./colors";

export const getCommonStyles = (colors: ThemeColors) => {
   const { height } = Dimensions.get("window");

   return StyleSheet.create({
      header: {
         flexDirection: "row",
         justifyContent: "center",
         alignItems: "center",
         paddingVertical: 15,
         paddingHorizontal: 20,
         backgroundColor: colors.background,
      },
      bottomSheet: {
         backgroundColor: colors.inputBackground,
         color: colors.text,
      },
      shadow: {
         shadowColor: "#000",
         shadowOffset: {
            width: 0,
            height: 1,
         },
         shadowOpacity: 0.2,
         shadowRadius: 1.41,
         elevation: 2,
      },
      logo: {
         ...Platform.select({
            web: {
               width: 200,
               height: 80,
            },
            default: {
               width: 100,
               height: 40,
            },
         }),
      },
      modalOverlay: {
         flex: 1,
         flexDirection: "row",
         backgroundColor: "rgba(0,0,0,0.3)",
      },
      formContainer: {
         width: "100%",
         maxWidth: 640,
         alignSelf: "center",
         padding: 10,
      },
      menuContainer: {
         height: height,
         width: 280,
         backgroundColor: colors.background,
         padding: 20,
         paddingTop: 90,
         borderTopRightRadius: 20,
         borderBottomRightRadius: 20,
         shadowColor: "#000",
         shadowOffset: { width: 0, height: 2 },
         shadowOpacity: 0.2,
         shadowRadius: 5,
         elevation: 5,
         justifyContent: "space-between",
         paddingBottom: 60,
      },
      menuItemRow: {
         flexDirection: "row",
         alignItems: "center",
         paddingVertical: 15,
      },
      icon: {
         marginRight: 12,
      },
      menuItem: {
         fontSize: 16,
      },
      spacer: {
         flex: 1,
      },
      profileSection: {
         borderTopWidth: 1,
         borderTopColor: "#ddd",
         paddingTop: 16,
         flexDirection: "row",
         alignItems: "center",
         gap: 12,
      },
      profileIcon: {
         width: 40,
         height: 40,
         borderRadius: 20,
         backgroundColor: "#eee",
         justifyContent: "center",
         alignItems: "center",
      },
      profileInitials: {
         fontSize: 40,
         fontWeight: "bold",
         color: "#333",
      },
      profileName: {
         fontWeight: "600",
         fontSize: 15,
         marginTop: 10,
         color: colors.text,
      },
      profileEmail: {
         fontSize: 12,
      },
      safeArea: {
         flex: 1,
         backgroundColor: colors.background,
      },
      keyboardAvoidingView: {
         flex: 1,
         backgroundColor: colors.background,
      },
      scrollViewContent: {
         flexGrow: 1,
         justifyContent: "center",
         paddingVertical: 20,
      },
      container: {
         flex: 1,
         justifyContent: "center",
         paddingHorizontal: 24,
      },
      logoContainer: {
         alignItems: "center",
         marginBottom: 40,
      },
      title: {
         fontSize: 26,
         color: colors.text,
         textAlign: "center",
         marginBottom: spacing.xl,
      },
      subtitle: {
         fontSize: 16,
         color: colors.icon,
         textAlign: "center",
         marginBottom: 40,
      },
      inputGroup: {
         flexDirection: "row",
         alignItems: "center",
         backgroundColor: colors.inputBackground,
         borderRadius: 12,
         marginBottom: 18,
         paddingHorizontal: 12,
         height: 52,
      },
      inputIcon: {
         marginRight: 10,
      },
      input: {
         flex: 1,
         height: "100%",
         fontSize: 16,
         color: colors.text,
         paddingHorizontal: 10,
      },
      eyeButton: {
         padding: 10,
      },
      forgotPasswordContainer: {
         alignSelf: "flex-end",
         marginBottom: 24,
      },
      forgotPasswordText: {
         fontSize: 14,
         color: Colors.light.tint,
         fontWeight: "500",
      },
      button: {
         backgroundColor: Colors.light.tint,
         paddingVertical: 16,
         borderRadius: 12,
         alignItems: "center",
         justifyContent: "center",
         minHeight: 52,
         opacity: 1,
         shadowColor: "#000",
         shadowOffset: { width: 0, height: 2 },
         shadowOpacity: 0.1,
         shadowRadius: 4,
         elevation: 3,
      },
      buttonPressed: {
         opacity: 0.85,
         transform: [{ scale: 0.98 }],
      },
      buttonDisabled: {
         opacity: 0.6,
         backgroundColor: colors.icon,
         shadowColor: "transparent",
         elevation: 0,
      },
      buttonText: {
         color: "#FFFFFF",
         fontSize: 16,
         fontWeight: "600",
      },
      footer: {
         flexDirection: "row",
         justifyContent: "center",
         marginTop: 32,
         paddingBottom: 10,
      },
      footerText: {
         color: colors.icon,
         fontSize: 14,
      },
      linkText: {
         color: Colors.light.tint,
         fontSize: 14,
         fontWeight: "600",
      },
      errorText: {
         color: Colors.light.error,
         fontSize: 12,
         marginTop: -10,
         marginBottom: 10,
         paddingLeft: 15,
      },
      apiErrorText: {
         textAlign: "center",
         fontWeight: "bold",
         marginBottom: 15,
      },
      iconContainer: {
         alignItems: "center",
         marginBottom: 30,
      },
      profileCircle: {
         width: 100,
         height: 100,
         borderRadius: 50,
         backgroundColor: "#ddd",
         justifyContent: "center",
         alignItems: "center",
         position: "relative",
      },
      cameraButton: {
         position: "absolute",
         bottom: 0,
         right: 0,
         backgroundColor: Colors.light.tint,
         width: 28,
         height: 28,
         borderRadius: 14,
         justifyContent: "center",
         alignItems: "center",
         borderWidth: 2,
         borderColor: "#fff",
      },
      cancelButton: {
         backgroundColor: "#A9A9A9",
         paddingVertical: 16,
         borderRadius: 12,
         alignItems: "center",
         justifyContent: "center",
         minHeight: 52,
         shadowColor: "#000",
         shadowOffset: { width: 0, height: 2 },
         shadowOpacity: 0.1,
         shadowRadius: 4,
         elevation: 3,
         marginTop: 12,
      },
      cancelButtonPressed: {
         opacity: 0.85,
         transform: [{ scale: 0.98 }],
      },
      cancelButtonText: {
         color: "#FFFFFF",
         fontSize: 16,
         fontWeight: "600",
      },
      userInfo: {
         fontSize: 16,
         marginBottom: 10,
         color: colors.text,
      },
      tokenInfo: {
         fontSize: 12,
         color: colors.icon,
         marginVertical: 10,
      },
      buttonContainer: {
         marginTop: 30,
      },
      tabBar: {
         flexDirection: "row",
         borderTopWidth: 1,
         height: 70,
         alignItems: "center",
         justifyContent: "space-around",
         paddingBottom: 15,
         paddingTop: 20,
         // marginBottom: 30,
      },
      tab: {
         flex: 1,
         alignItems: "center",
         justifyContent: "center",
      },
      iconLabelWrapper: {
         alignItems: "center",
         justifyContent: "center",
      },
      avatarContainer: {
         alignItems: "center",
         marginTop: 40,
         marginBottom: 32,
      },
      card: {
         marginHorizontal: 24,
         backgroundColor: colors.card,
         borderRadius: 14,
         shadowOpacity: Platform.OS === "android" ? 0.13 : 0.06,
         shadowRadius: 8,
         elevation: 4,
         paddingVertical: 2,
         paddingHorizontal: 0,
      },
      row: {
         flexDirection: "row",
         alignItems: "center",
         paddingVertical: 18,
         paddingHorizontal: 18,
      },
      iconLeft: {
         marginRight: 16,
      },
      rowTitle: {
         fontSize: 17,
         fontWeight: "500",
         color: colors.text,
         flex: 1,
      },
      arrowRight: {
         marginLeft: 6,
      },
      arrowRightFar: {
         marginLeft: 30,
      },
      divider: {
         height: 1,
         backgroundColor: colors.icon + "14",
         marginHorizontal: 10,
      },
      logoutRow: {
         flexDirection: "row",
         alignItems: "center",
         paddingVertical: 18,
         paddingHorizontal: 18,
      },
      logoutText: {
         fontSize: 17,
         fontWeight: "500",
         color: colors.error,
         flex: 1,
      },
   });
};
