import React from "react";
import { View, Text, StyleSheet } from "react-native";

import { spacing } from "@/constants/spacing";
import { ThemeColors, useTheme } from "@/context/ThemeContext";

interface Reading {
   id: string;
   systolic: number;
   diastolic: number;
   pulse: number;
   timestamp: string;
}

interface ReadingCardProps {
   reading: Reading;
}

const ReadingCard: React.FC<ReadingCardProps> = ({ reading }) => {
   const { colors } = useTheme();
   const styles = getStyles(colors);

   const readingDate = new Date(reading.timestamp);
   const formattedDate = `${readingDate.toLocaleDateString()} ${readingDate.toLocaleTimeString()}`;

   return (
      <View style={styles.card}>
         <View style={styles.header}>
            <Text style={styles.headerText}>Blood Pressure</Text>
            <Text style={styles.dateText}>{formattedDate}</Text>
         </View>
         <View style={styles.readings}>
            <View style={styles.reading}>
               <Text style={styles.readingValue}>{reading.systolic}</Text>
               <Text style={styles.readingUnit}>sys</Text>
            </View>
            <View style={styles.reading}>
               <Text style={styles.readingValue}>{reading.diastolic}</Text>
               <Text style={styles.readingUnit}>dia</Text>
            </View>
            <View style={styles.reading}>
               <Text style={styles.readingValue}>{reading.pulse}</Text>
               <Text style={styles.readingUnit}>pul</Text>
            </View>
         </View>
      </View>
   );
};

const getStyles = (colors: ThemeColors) =>
   StyleSheet.create({
      card: {
         backgroundColor: colors.card,
         borderRadius: 12,
         padding: spacing.md,
      },
      header: {
         flexDirection: "row",
         justifyContent: "space-between",
         alignItems: "center",
         marginBottom: spacing.lg,
      },
      headerText: {
         fontSize: 18,
         fontWeight: "bold",
         color: colors.text,
      },
      dateText: {
         fontSize: 14,
         color: colors.textMuted,
      },
      readings: {
         flexDirection: "row",
         justifyContent: "space-around",
      },
      reading: {
         alignItems: "center",
         gap: spacing.xs,
      },
      readingValue: {
         fontSize: 24,
         fontWeight: "bold",
         color: colors.text,
      },
      readingUnit: {
         fontSize: 14,
         color: colors.textMuted,
         textTransform: "uppercase",
      },
   });

export default ReadingCard;
