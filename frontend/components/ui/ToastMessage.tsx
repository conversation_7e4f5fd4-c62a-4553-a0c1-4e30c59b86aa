import React, { useEffect, useRef } from "react";
import { Animated, StyleSheet, Text } from "react-native";
import MaterialIcons from "@expo/vector-icons/MaterialIcons";

interface ToastProps {
   visible: boolean;
   message: string;
   onHide?: () => void;
   type?: "success" | "error";
}

const ToastMessage: React.FC<ToastProps> = ({
   visible,
   message,
   onHide,
   type = "error",
}) => {
   const slideAnim = useRef(new Animated.Value(-100)).current;

   useEffect(() => {
      if (visible) {
         slideAnim.setValue(-100); // Reset before animating

         const animation = Animated.sequence([
            Animated.timing(slideAnim, {
               toValue: 0,
               duration: 300,
               useNativeDriver: true,
            }),
            Animated.delay(4500),
            Animated.timing(slideAnim, {
               toValue: -150,
               duration: 300,
               useNativeDriver: true,
            }),
         ]);

         animation.start(() => {
            onHide?.();
         });

         return () => animation.stop(); // Cancel on unmount or rerun
      }
   }, [visible, message]);

   if (!visible) return null;

   const isSuccess = type === "success";
   const backgroundColor = isSuccess ? "#388E3C" : "#D32F2F";
   const iconName = isSuccess ? "check-circle-outline" : "error-outline";

   return (
      <Animated.View
         style={[
            styles.toastContainer,
            { backgroundColor, transform: [{ translateY: slideAnim }] },
         ]}
      >
         <MaterialIcons
            name={iconName}
            size={24}
            color="white"
            style={styles.icon}
         />
         <Text style={styles.toastText}>{message}</Text>
      </Animated.View>
   );
};

const styles = StyleSheet.create({
   toastContainer: {
      position: "absolute",
      top: 50,
      left: 20,
      right: 20,
      padding: 15,
      borderRadius: 8,
      flexDirection: "row",
      alignItems: "center",
      zIndex: 1000,
      elevation: 5,
   },
   toastText: {
      color: "white",
      fontSize: 16,
      flex: 1,
   },
   icon: {
      marginRight: 10,
   },
});

export default ToastMessage;
