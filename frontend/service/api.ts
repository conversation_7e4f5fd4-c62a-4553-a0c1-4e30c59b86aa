import { getItemAsync } from "expo-secure-store";

const API_BASE_URL = process.env.EXPO_PUBLIC_API_URL;

let tokenExpiryHandler: (() => Promise<void>) | null = null;

export const setTokenExpiryHandler = (fn: () => Promise<void>) => {
   tokenExpiryHandler = fn;
};

// TODO: Should token/auth related functions here in api.tsx
async function getToken(): Promise<string | null> {
   return await getItemAsync("accessToken");
}

async function retry<TResponse>(
   originalCall: (token: string) => Promise<TResponse>,
): Promise<TResponse> {
   if (!tokenExpiryHandler) {
      console.warn("No tokenExpiryHandler set cannot refresh.");
      throw new Error("Access token expired and no refresh handler provided.");
   }

   console.log("Access token expired running refresh path …");
   await tokenExpiryHandler();

   const freshToken = await getToken();
   if (!freshToken) {
      console.error("Refresh failed still no token");
      throw new Error("Token refresh failed.");
   }

   console.log("Retrying original request with new token");
   return originalCall(freshToken);
}

export const apiService = {
   post: async <TResponse, TRequest = Record<string, any>>(
      endpoint: string,
      data: TRequest,
      token?: string,
   ): Promise<TResponse> => {
      try {
         const headers: HeadersInit = {
            "Content-Type": "application/json",
         };
         if (token) headers["Authorization"] = `Bearer ${token}`;

         const response = await fetch(`${API_BASE_URL}${endpoint}`, {
            method: "POST",
            headers,
            body: JSON.stringify(data),
         });

         if (
            response.status === 401 &&
            tokenExpiryHandler &&
            endpoint !== "/users/login" &&
            endpoint !== "/users/logout"
         ) {
            return retry((newToken) =>
               apiService.post(endpoint, data, newToken),
            );
         }

         if (!response.ok) {
            const errorText = await response.text();
            let errorMessage = errorText;
            try {
               const errorJson = JSON.parse(errorMessage);
               errorMessage =
                  errorJson.message ||
                  errorJson.error ||
                  errorJson.detail ||
                  errorText;
            } catch (e) {
               console.log(`api at post ${endpoint} ${e}`);
            }
            throw new Error(
               errorMessage ||
                  `HTTP error ${response.status}: ${response.statusText}`,
            );
         }

         const contentType = response.headers.get("content-type");
         if (contentType?.includes("application/json")) {
            return response.json() as Promise<TResponse>;
         } else {
            const textResponse = await response.text();
            return textResponse as TResponse;
         }
      } catch (error) {
         console.warn(`API POST Error (${endpoint}):`, error);
         throw new Error(
            error instanceof Error
               ? error.message
               : "An unknown network error occurred.",
         );
      }
   },

   patch: async <TResponse, TRequest = Record<string, any>>(
      endpoint: string,
      data: TRequest,
      token?: string,
   ): Promise<TResponse> => {
      try {
         const headers: HeadersInit = {
            "Content-Type": "application/json",
         };
         if (token) headers["Authorization"] = `Bearer ${token}`;

         const response = await fetch(`${API_BASE_URL}${endpoint}`, {
            method: "PATCH",
            headers,
            body: JSON.stringify(data),
         });

         if (response.status === 401 && tokenExpiryHandler) {
            return retry((newToken) =>
               apiService.patch(endpoint, data, newToken),
            );
         }

         if (!response.ok) throw new Error(await response.text());

         return response.json() as Promise<TResponse>;
      } catch (e) {
         throw e;
      }
   },

   get: async <TResponse>(
      endpoint: string,
      token?: string,
   ): Promise<TResponse> => {
      try {
         const headers: HeadersInit = {
            "Content-Type": "application/json",
         };
         if (token) headers["Authorization"] = `Bearer ${token}`;

         const response = await fetch(`${API_BASE_URL}${endpoint}`, {
            method: "GET",
            headers,
         });

         if (response.status === 401 && tokenExpiryHandler) {
            return retry((newToken) => apiService.get(endpoint, newToken));
         }

         if (!response.ok) {
            const errorText = await response.text();
            let errorMessage = errorText;
            try {
               const errorJson = JSON.parse(errorText);
               errorMessage =
                  errorJson.message ||
                  errorJson.error ||
                  errorJson.detail ||
                  errorText;
            } catch (e) {
               console.log(`api at get ${e}`);
            }
            throw new Error(
               errorMessage ||
                  `HTTP error ${response.status}: ${response.statusText}`,
            );
         }

         return response.json() as Promise<TResponse>;
      } catch (error) {
         console.warn(`API GET Error (${endpoint}):`, error);
         throw new Error(
            error instanceof Error
               ? error.message
               : "An unknown network error occurred.",
         );
      }
   },
};
