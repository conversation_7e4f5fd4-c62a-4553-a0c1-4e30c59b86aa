<template>
    <div id="spring-test-wrapper">
      <h1>This is a Spring Test page!</h1>
      <div>
        <button @click="CheckConnection">
          Check connection
        </button> 
        <span id="check-respons" :style="responseStyle">{{ response }}</span>
      </div>
    </div>
</template>
  
<script>
  export default {
    data() {
      return {
        response: null,
      };
    },
    computed: {
      responseStyle() {
        return {
          color: this.response === 'ok!' ? 'green' : 'red',
          marginLeft: '10px',
          fontWeight: 'bold',
        };
      },
    },
    methods: {
      async CheckConnection() {
        try {
          const response = await fetch(`${import.meta.env.VITE_BACKEND_URL}/api/spring-test`);
          if (!response.ok) {
            throw new Error('!ok');
          }
          const data = await response.json();
          
          this.response = data.data;
        } catch (error) {
          console.error('Error:', error);
          this.response = '!ok';
        }
      },
    },
  };
</script>