import { StyleSheet, View, Text } from "react-native";

import { useAuth } from "@/context/AuthContext";
import { ThemeColors, useTheme } from "@/context/ThemeContext";
// import { ECGModule } from "@ihealth/ihealthlibrary-react-native";

export default function AppIndexScreen() {
   const { user, accessToken } = useAuth();
   const { colors } = useTheme();
   const styles = getStyles(colors);

   // console.log(ECGModule);

   return (
      <View style={styles.container}>
         <Text style={styles.title}>Welcome to the App!</Text>
         {user && (
            <>
               <Text style={styles.userInfo}>
                  Name: {user.firstName} {user.lastName}
               </Text>
               <Text style={styles.userInfo}>Email: {user.email}</Text>
               <Text style={styles.userInfo}>User ID: {user.id}</Text>
            </>
         )}
         {accessToken && (
            <Text style={styles.tokenInfo}>Access Token present</Text>
         )}
      </View>
   );
}

const getStyles = (colors: ThemeColors) =>
   StyleSheet.create({
      container: {
         flex: 1,
         justifyContent: "center",
         alignItems: "center",
         padding: 20,
         backgroundColor: colors.background,
      },
      title: {
         fontSize: 24,
         fontWeight: "bold",
         marginBottom: 20,
         color: colors.text,
      },
      userInfo: {
         fontSize: 16,
         marginBottom: 10,
         color: colors.text,
      },
      tokenInfo: {
         fontSize: 12,
         color: colors.icon,
         marginVertical: 10,
      },
      buttonContainer: {
         marginTop: 30,
      },
   });
