import { useMemo } from "react";
import { Slot } from "expo-router";
import { View, StyleSheet } from "react-native";

import Header from "@/components/ui/Header";
import Navigation from "@/components/ui/Navigation";
import { ThemeColors, useTheme } from "@/context/ThemeContext";

const tabs = [
   { label: "Summary", icon: "home-outline", iconFocused: "home", route: "/" },
   {
      label: "Browse",
      icon: "search-outline",
      iconFocused: "search",
      route: "",
   },
   {
      label: "Devices",
      icon: "phone-portrait-outline",
      iconFocused: "phone-portrait",
      route: "/devices",
   },
   { label: "Sharing", icon: "share-outline", iconFocused: "share", route: "" },
   {
      label: "Profile",
      icon: "person-circle-outline",
      iconFocused: "person-circle",
      route: "/profile",
   },
];

const AppLayout: React.FC = () => {
   const { colors } = useTheme();
   const styles = useMemo(() => getStyles(colors), [colors]);

   return (
      <View style={styles.container}>
         {/* <Header /> */}
         <View style={styles.content}>
            <Slot />
         </View>
         <Navigation tabs={tabs} />
      </View>
   );
};

const getStyles = (colors: ThemeColors) => {
   return StyleSheet.create({
      container: {
         flex: 1,
         backgroundColor: colors.background,
         paddingTop: 50,
      },
      content: {
         flex: 1,
      },
   });
};

export default AppLayout;
