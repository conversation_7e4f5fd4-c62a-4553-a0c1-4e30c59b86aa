import React, { useState } from "react";
import {
   View,
   Text,
   TextInput,
   SafeAreaView,
   KeyboardAvoidingView,
   Platform,
   Pressable,
   ActivityIndicator,
   ScrollView,
   Alert,
} from "react-native";

import { useAuth } from "@/context/AuthContext";
import MaterialIcons from "@expo/vector-icons/MaterialIcons";
import { apiService } from "@/service/api";
import ToastMessage from "@/components/ui/ToastMessage";
import { Colors } from "@/constants/colors";
import { getCommonStyles } from "@/constants/styles";
import { useRouter } from "expo-router";
import { useTheme } from "@/context/ThemeContext";

const ChangePasswordScreen: React.FC = () => {
   const { signOut, accessToken } = useAuth();
   const { colors } = useTheme();
   const styles = getCommonStyles(colors);
   const router = useRouter();

   const [currentPassword, setCurrentPassword] = useState("");
   const [newPassword, setNewPassword] = useState("");
   const [isLoading, setIsLoading] = useState(false);
   const [currentError, setCurrentError] = useState<string | null>(null);
   const [newError, setNewError] = useState<string | null>(null);
   const [apiError, setApiError] = useState<string | null>(null);
   const [toastVisible, setToastVisible] = useState(false);
   const [toastMessage, setToastMessage] = useState("");
   const [toastType, setToastType] = useState<"success" | "error">("error");
   const [showCurrentPassword, setShowCurrentPassword] = useState(false);
   const [showNewPassword, setShowNewPassword] = useState(false);

   const validateForm = () => {
      let valid = true;
      setCurrentError(null);
      setNewError(null);
      setApiError(null);
      setToastVisible(false);

      if (!currentPassword.trim()) {
         setToastMessage("Current password is required");
         setToastVisible(true);
         valid = false;
      }
      if (!newPassword.trim()) {
         setToastMessage("New password is required");
         setToastVisible(true);
         valid = false;
      } else if (newPassword.length < 6) {
         setToastMessage("New password must be at least 6 characters");
         setToastVisible(true);
         valid = false;
      }

      return valid;
   };

   const handleChange = async () => {
      if (!validateForm()) return;

      setIsLoading(true);
      try {
         if (!accessToken) {
            setToastMessage("Authentication token is missing");
            setToastVisible(true);
            return;
         }
         await apiService.post(
            "/change-password",
            {
               current_password: currentPassword,
               new_password: newPassword,
            },
            accessToken,
         );
         setToastMessage("Password changed successfully");
         setToastType("success");
         setToastVisible(true);
         setCurrentPassword("");
         setNewPassword("");
         setTimeout(() => {
            setToastVisible(false);
            router.replace("/profile");
         }, 1200);
      } catch (error: any) {
         let message;
         if (error instanceof Error) {
            message = error.message;

            // TODO: Do NOT depend on the string content of error messages, that's why we have error codes...
            if (error.message.includes("Failed to refresh token")) {
               message = "Your session has expired";
               await signOut();
            }
         } else message = "Unexpected errror";

         setApiError(message);
         setToastMessage("Failed to change password");
         setToastVisible(true);
         Alert.alert("Couldn't change password", message);
      } finally {
         setIsLoading(false);
      }
   };

   return (
      <SafeAreaView style={styles.safeArea}>
         <KeyboardAvoidingView
            behavior={Platform.OS === "ios" ? "padding" : "height"}
            style={styles.keyboardAvoidingView}
         >
            <ToastMessage
               visible={toastVisible}
               message={toastMessage}
               onHide={() => setToastVisible(false)}
               type={toastType}
            />
            <ScrollView
               contentContainerStyle={styles.scrollViewContent}
               keyboardShouldPersistTaps="handled"
            >
               <View style={styles.formContainer}>
                  <Text style={styles.title}>Change Password</Text>
                  {apiError && (
                     <Text style={[styles.errorText, styles.apiErrorText]}>
                        {apiError}
                     </Text>
                  )}

                  <View style={styles.inputGroup}>
                     <MaterialIcons
                        name="lock"
                        size={20}
                        color={colors.icon}
                        style={styles.inputIcon}
                     />
                     <TextInput
                        style={styles.input}
                        placeholder="Current Password"
                        placeholderTextColor={colors.icon}
                        secureTextEntry={!showCurrentPassword}
                        value={currentPassword}
                        onChangeText={setCurrentPassword}
                        textContentType="password"
                        autoComplete="password"
                     />
                     <Pressable
                        onPress={() =>
                           setShowCurrentPassword(!showCurrentPassword)
                        }
                        style={styles.eyeButton}
                     >
                        <MaterialIcons
                           name={
                              showCurrentPassword
                                 ? "visibility-off"
                                 : "visibility"
                           }
                           size={20}
                           color={colors.icon}
                        />
                     </Pressable>
                  </View>
                  {currentError && (
                     <Text style={styles.errorText}>{currentError}</Text>
                  )}

                  <View style={styles.inputGroup}>
                     <MaterialIcons
                        name="lock-outline"
                        size={20}
                        color={colors.icon}
                        style={styles.inputIcon}
                     />
                     <TextInput
                        style={styles.input}
                        placeholder="New Password"
                        placeholderTextColor={colors.icon}
                        secureTextEntry={!showNewPassword}
                        value={newPassword}
                        onChangeText={setNewPassword}
                        textContentType="newPassword"
                        autoComplete="password-new"
                     />
                     <Pressable
                        onPress={() => setShowNewPassword(!showNewPassword)}
                        style={styles.eyeButton}
                     >
                        <MaterialIcons
                           name={
                              showNewPassword ? "visibility-off" : "visibility"
                           }
                           size={20}
                           color={colors.icon}
                        />
                     </Pressable>
                  </View>
                  {newError && <Text style={styles.errorText}>{newError}</Text>}

                  <Pressable
                     style={({ pressed }) => [
                        styles.button,
                        pressed && styles.buttonPressed,
                        isLoading && styles.buttonDisabled,
                     ]}
                     onPress={handleChange}
                     disabled={isLoading}
                  >
                     {isLoading ? (
                        <ActivityIndicator color={Colors.dark.text} />
                     ) : (
                        <Text style={styles.buttonText}>Submit</Text>
                     )}
                  </Pressable>
                  <Pressable
                     style={({ pressed }) => [
                        styles.cancelButton,
                        pressed && styles.cancelButtonPressed,
                     ]}
                     onPress={() => {
                        router.back();
                     }}
                  >
                     <Text style={styles.cancelButtonText}>Cancel</Text>
                  </Pressable>
               </View>
            </ScrollView>
         </KeyboardAvoidingView>
      </SafeAreaView>
   );
};

export default ChangePasswordScreen;
