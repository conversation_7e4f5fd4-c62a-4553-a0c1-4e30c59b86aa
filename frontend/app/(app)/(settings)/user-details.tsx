import React, { useState } from "react";
import {
   View,
   Text,
   TextInput,
   SafeAreaView,
   KeyboardAvoidingView,
   ScrollView,
   Platform,
   Pressable,
   Modal,
   TouchableOpacity,
   StyleSheet,
} from "react-native";
import { useRouter } from "expo-router";
import MaterialIcons from "@expo/vector-icons/MaterialIcons";
import * as SecureStore from "expo-secure-store";

import { useAuth } from "@/context/AuthContext";
import { getCommonStyles } from "@/constants/styles";
import { apiService } from "@/service/api";
import { BackendUser } from "@/types/auth";
import ToastMessage from "@/components/ui/ToastMessage";
import { useTheme } from "@/context/ThemeContext";

type Gender = "MALE" | "FEMALE" | "OTHER";
const genderOptions = [
   { label: "Male", value: "MALE" },
   { label: "Female", value: "FEMALE" },
   { label: "Other", value: "OTHER" },
];

const UserDetails: React.FC = () => {
   const { colors } = useTheme();
   const styles = getCommonStyles(colors);
   const router = useRouter();
   const { user, getAccessToken, updateUser, signOut } = useAuth();
   const [toastVisible, setToastVisible] = useState(false);
   const [toastMessage, setToastMessage] = useState("");
   const [toastType, setToastType] = useState<"success" | "error">("error");

   const [firstName, setFirstName] = useState(user?.firstName ?? "");
   const [lastName, setLastName] = useState(user?.lastName ?? "");
   const [email, setEmail] = useState(user?.email ?? "");
   const [dob, setDob] = useState(user?.dob ?? "");
   const [gender, setGender] = useState<Gender | "">(user?.gender ?? "");

   const [showGenderModal, setShowGenderModal] = useState(false);

   const handleUpdateProfile = async () => {
      if (
         !firstName.trim() ||
         !lastName.trim() ||
         !email.trim() ||
         !dob.trim() ||
         !gender
      ) {
         setToastMessage("All fields are required.");
         setToastVisible(true);
         setToastType("error");
         return;
      }
      try {
         const accessToken = await getAccessToken();
         if (!user?.id || !accessToken) {
            setToastMessage("No user id or access token");
            setToastVisible(true);
            setToastType("error");
            return;
         }

         const updatedUser = await apiService.patch<BackendUser>(
            `/users/${user.id}`,
            { firstName, lastName, email, dob, gender },
            accessToken,
         );

         await SecureStore.setItemAsync(
            "userInfo",
            JSON.stringify(updatedUser),
         );
         updateUser(updatedUser);

         setFirstName(updatedUser.firstName);
         setLastName(updatedUser.lastName);
         setEmail(updatedUser.email);
         setDob(updatedUser.dob);
         setGender(updatedUser.gender);

         setToastMessage("Profile updated successfully!");
         setToastType("success");
         setToastVisible(true);
         setTimeout(() => {
            setToastVisible(false);
            router.replace("/profile");
         }, 1200);
      } catch (error) {
         const hasStatus = (err: any): err is { status: number } =>
            typeof err === "object" && err !== null && "status" in err;
         if (
            (error instanceof Error &&
               /expired|unauthorized|invalid token/i.test(error.message)) ||
            (hasStatus(error) && (error.status === 401 || error.status === 403))
         ) {
            setToastMessage("Session expired. Please log in again.");
            setToastVisible(true);
            setToastType("error");

            setTimeout(() => {
               setToastVisible(false);
               signOut();
               router.replace("/login");
            }, 1000);
            return;
         }
      }
   };

   const handleGenderSelect = (value: Gender) => {
      setGender(value);
      setShowGenderModal(false);
   };

   const modalStyles = StyleSheet.create({
      overlay: {
         flex: 1,
         backgroundColor: "rgba(0,0,0,0.5)",
         justifyContent: "center",
         alignItems: "center",
      },
      modalContainer: {
         backgroundColor: colors.background,
         borderRadius: 12,
         padding: 20,
         width: "85%",
         maxWidth: 300,
         alignItems: "center",
         shadowColor: "#000",
         shadowOffset: { width: 0, height: 2 },
         shadowOpacity: 0.25,
         shadowRadius: 4,
         elevation: 5,
      },
      modalTitle: {
         fontSize: 18,
         fontWeight: "600",
         color: colors.text,
         marginBottom: 15,
      },
      option: {
         flexDirection: "row",
         alignItems: "center",
         justifyContent: "space-between",
         paddingVertical: 12,
         paddingHorizontal: 10,
         width: "100%",
         borderRadius: 8,
      },
      optionText: {
         fontSize: 16,
      },
   });

   return (
      <SafeAreaView style={styles.safeArea}>
         <KeyboardAvoidingView
            behavior={Platform.OS === "ios" ? "padding" : "height"}
            style={styles.keyboardAvoidingView}
         >
            <ToastMessage
               visible={toastVisible}
               message={toastMessage}
               onHide={() => setToastVisible(false)}
               type={toastType}
            />
            <ScrollView
               contentContainerStyle={styles.scrollViewContent}
               keyboardShouldPersistTaps="handled"
            >
               <View style={styles.formContainer}>
                  <Text style={styles.title}>User Details</Text>
                  <View style={styles.inputGroup}>
                     <MaterialIcons
                        name="person"
                        size={20}
                        color={colors.icon}
                        style={styles.inputIcon}
                     />
                     <TextInput
                        style={styles.input}
                        placeholder="First Name"
                        placeholderTextColor={colors.icon}
                        value={firstName}
                        editable={true}
                        onChangeText={setFirstName}
                     />
                  </View>
                  <View style={styles.inputGroup}>
                     <MaterialIcons
                        name="person-outline"
                        size={20}
                        color={colors.icon}
                        style={styles.inputIcon}
                     />
                     <TextInput
                        style={styles.input}
                        placeholder="Last Name"
                        placeholderTextColor={colors.icon}
                        value={lastName}
                        editable={true}
                        onChangeText={setLastName}
                     />
                  </View>
                  <View style={styles.inputGroup}>
                     <MaterialIcons
                        name="email"
                        size={20}
                        color={colors.icon}
                        style={styles.inputIcon}
                     />
                     <TextInput
                        style={styles.input}
                        placeholder="Email"
                        placeholderTextColor={colors.icon}
                        value={email}
                        editable={true}
                        onChangeText={setEmail}
                        keyboardType="email-address"
                     />
                  </View>
                  <View style={styles.inputGroup}>
                     <MaterialIcons
                        name="cake"
                        size={20}
                        color={colors.icon}
                        style={styles.inputIcon}
                     />
                     <TextInput
                        style={styles.input}
                        placeholder="Date of Birth (YYYY-MM-DD)"
                        placeholderTextColor={colors.icon}
                        value={dob}
                        editable={true}
                        onChangeText={setDob}
                     />
                  </View>
                  <View style={styles.inputGroup}>
                     <MaterialIcons
                        name="wc"
                        size={20}
                        color={colors.icon}
                        style={styles.inputIcon}
                     />
                     <Pressable
                        style={[styles.input, { justifyContent: "center" }]}
                        onPress={() => setShowGenderModal(true)}
                     >
                        <Text
                           style={{
                              color: gender ? colors.text : colors.icon,
                              fontSize: 16,
                           }}
                        >
                           {gender
                              ? genderOptions.find(
                                   (opt) => opt.value === gender,
                                )?.label
                              : "Select Gender"}
                        </Text>
                     </Pressable>
                     <Pressable
                        onPress={() => setShowGenderModal(true)}
                        style={{ marginLeft: 8 }}
                     >
                        <MaterialIcons
                           name="arrow-drop-down"
                           size={24}
                           color={colors.icon}
                        />
                     </Pressable>
                  </View>
                  <Modal
                     visible={showGenderModal}
                     transparent
                     animationType="slide"
                     onRequestClose={() => setShowGenderModal(false)}
                  >
                     <TouchableOpacity
                        style={modalStyles.overlay}
                        activeOpacity={1}
                        onPressOut={() => setShowGenderModal(false)}
                     >
                        <View style={modalStyles.modalContainer}>
                           <Text style={modalStyles.modalTitle}>
                              Select Gender
                           </Text>
                           {genderOptions.map((opt) => (
                              <TouchableOpacity
                                 key={opt.value}
                                 style={modalStyles.option}
                                 onPress={() =>
                                    handleGenderSelect(opt.value as Gender)
                                 }
                              >
                                 <Text
                                    style={[
                                       modalStyles.optionText,
                                       {
                                          color:
                                             gender === opt.value
                                                ? colors.tint
                                                : colors.text,
                                          fontWeight:
                                             gender === opt.value
                                                ? "bold"
                                                : "normal",
                                       },
                                    ]}
                                 >
                                    {opt.label}
                                 </Text>
                                 {gender === opt.value && (
                                    <MaterialIcons
                                       name="check"
                                       size={20}
                                       color={colors.tint}
                                       style={{ marginLeft: 10 }}
                                    />
                                 )}
                              </TouchableOpacity>
                           ))}
                        </View>
                     </TouchableOpacity>
                  </Modal>
                  <Pressable
                     style={({ pressed }) => [
                        styles.button,
                        pressed && styles.buttonPressed,
                     ]}
                     onPress={handleUpdateProfile}
                  >
                     <Text style={styles.buttonText}>Update</Text>
                  </Pressable>
                  <Pressable
                     style={({ pressed }) => [
                        styles.cancelButton,
                        pressed && styles.cancelButtonPressed,
                     ]}
                     onPress={() => {
                        setFirstName(user?.firstName ?? "");
                        setLastName(user?.lastName ?? "");
                        setEmail(user?.email ?? "");
                        setDob(user?.dob ?? "");
                        setGender(user?.gender ?? "");
                        router.back();
                     }}
                  >
                     <Text style={styles.cancelButtonText}>Cancel</Text>
                  </Pressable>
               </View>
            </ScrollView>
         </KeyboardAvoidingView>
      </SafeAreaView>
   );
};

export default UserDetails;
