import React, {
   useCallback,
   useEffect,
   useMemo,
   useRef,
   useState,
} from "react";
import {
   View,
   Text,
   ScrollView,
   SafeAreaView,
   StyleSheet,
   Pressable,
   TouchableOpacity,
} from "react-native";
import { GestureHandlerRootView } from "react-native-gesture-handler";
import BottomSheet, { BottomSheetView } from "@gorhom/bottom-sheet";
import { ProgressBar } from "react-native-paper";
import { Image } from "expo-image";
import { useRouter } from "expo-router";

import { spacing } from "@/constants/spacing";
import { apiService } from "@/service/api";
import DeviceCard from "@/components/ui/DeviceCard";
import { ThemeColors, useTheme } from "@/context/ThemeContext";
import { getCommonStyles } from "@/constants/styles";

interface Device {
   id: string;
   name: string;
   brand: string;
   type: string;
   readings: number;
   imageUrl: string;
   backgroundColor: string;
}

type PairingStatus = "PAIRING" | "PAIRED" | "READY" | "NOTFOUND";

const DevicesScreen: React.FC = () => {
   const { colors } = useTheme();
   const commonStyles = getCommonStyles(colors);
   const styles = useMemo(() => getStyles(colors), [colors]);
   const router = useRouter();

   const [devices, setDevices] = useState<Device[]>([]);
   const [pairingStatus, setPairingStatus] = useState<PairingStatus>("READY");
   const [pairedDevice, setPairedDevice] = useState<Device | null>(null);

   const bottomSheetRef = useRef<BottomSheet>(null);

   const sheetIndexMap = {
      READY: -1,
      PAIRING: 1,
      PAIRED: 2,
      NOTFOUND: 1,
   };

   useEffect(() => {
      const fetchData = async () => {
         const response = await apiService.get<Device[]>("/devices");
         setDevices(response);
      };

      fetchData();
   }, []);

   useEffect(() => {
      let index = sheetIndexMap[pairingStatus];
      if (index == -1) bottomSheetRef.current?.close();
      else bottomSheetRef.current?.snapToIndex(index);
   }, [pairingStatus]);

   const handlePairDevice = async () => {
      if (pairingStatus !== "READY") return;

      setPairingStatus("PAIRING");

      setTimeout(() => {
         setPairingStatus("PAIRED");
      }, 7500);
   };

   const handleSheetChanges = useCallback(
      (index: number) => {
         if (index !== 2 && pairingStatus === "PAIRED")
            setPairingStatus("READY");
      },
      [pairingStatus],
   );

   return (
      <GestureHandlerRootView>
         <ScrollView contentContainerStyle={styles.scrollContainer}>
            <View style={styles.container}>
               <Text style={styles.title}>Devices</Text>

               <View style={styles.header}>
                  <Pressable
                     style={({ pressed }) => [
                        commonStyles.button,
                        pressed && commonStyles.buttonPressed,
                        pairingStatus !== "READY" &&
                           commonStyles.buttonDisabled,
                     ]}
                     onPress={handlePairDevice}
                  >
                     <Text style={commonStyles.buttonText}>Pair a device</Text>
                  </Pressable>
               </View>

               <View style={styles.devicesContainer}>
                  {devices.map((device) => (
                     <TouchableOpacity
                        key={device.id}
                        onPress={() => router.push("/(app)/reading-list")}
                     >
                        <DeviceCard device={device} />
                     </TouchableOpacity>
                  ))}
               </View>
            </View>
         </ScrollView>

         <BottomSheet
            ref={bottomSheetRef}
            onChange={handleSheetChanges}
            index={-1}
            snapPoints={["25%", "50%"]}
            handleStyle={commonStyles.bottomSheet}
            handleIndicatorStyle={{ backgroundColor: colors.text }}
            backgroundStyle={commonStyles.bottomSheet}
         >
            <BottomSheetView>
               {pairingStatus === "PAIRING" && (
                  <View style={styles.drawerContainer}>
                     <Text style={styles.drawerTitle}>
                        Searching for devices
                     </Text>
                     <Text style={styles.drawerText}>
                        Make sure that your device is in pairing mode and
                        nearby.
                     </Text>
                     <ProgressBar
                        progress={0.5}
                        indeterminate
                        style={styles.drawerProgressBar}
                     />
                  </View>
               )}

               {pairingStatus === "PAIRED" && (
                  <View style={styles.drawerContainer}>
                     <Text style={styles.drawerTitle}>Device Paired</Text>
                     <Image
                        style={styles.deviceImage}
                        source="http://192.168.68.71:8081/files/BP792IT.jpg"
                     ></Image>
                     <Text style={styles.drawerText}>
                        Your Blood Pressure Monitor is now paired with your
                        account.
                     </Text>
                     <Pressable
                        style={({ pressed }) => [
                           commonStyles.button,
                           pressed && commonStyles.buttonPressed,
                           { width: "100%", maxWidth: 120 },
                        ]}
                        onPress={() => setPairingStatus("READY")}
                     >
                        <Text style={commonStyles.buttonText}>Done</Text>
                     </Pressable>
                  </View>
               )}
            </BottomSheetView>
         </BottomSheet>
      </GestureHandlerRootView>
   );
};

const getStyles = (colors: ThemeColors) =>
   StyleSheet.create({
      deviceImage: {
         flex: 1,
         width: 120,
         height: 120,
         borderRadius: 12,
         justifyContent: "center",
         alignItems: "center",
         marginBottom: spacing.md,
      },
      scrollContainer: {
         flexGrow: 1,
      },
      header: {
         marginBottom: spacing.xl,
      },
      drawerTitle: {
         fontSize: 22,
         fontWeight: "bold",
         color: colors.text,
         marginBottom: spacing.sm,
      },
      drawerText: {
         color: colors.text,
         textAlign: "center",
         marginBottom: spacing.xl,
      },
      drawerProgressBar: {
         width: 250,
         marginBottom: spacing.md,
      },
      drawerContainer: {
         flex: 1,
         width: "100%",
         paddingHorizontal: spacing.lg,
         paddingTop: spacing.md,
         justifyContent: "center",
         alignItems: "center",
      },
      container: {
         flex: 1,
         backgroundColor: colors.background,
         paddingHorizontal: spacing.lg,
         paddingTop: spacing.md,
      },
      title: {
         fontSize: 28,
         fontWeight: "bold",
         color: colors.text,
         textAlign: "center",
         marginBottom: spacing.xl,
      },
      devicesContainer: {
         gap: spacing.md,
      },
   });

export default DevicesScreen;
