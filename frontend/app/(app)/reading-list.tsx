import React, { useEffect, useState } from "react";
import {
   View,
   Text,
   ScrollView,
   SafeAreaView,
   StyleSheet,
   Pressable,
   TouchableOpacity,
} from "react-native";

import { spacing } from "@/constants/spacing";
import ReadingCard from "@/components/ui/ReadingCard";
import { getCommonStyles } from "@/constants/styles";
import { useRouter } from "expo-router";
import { apiService } from "@/service/api";
import { useAuth } from "@/context/AuthContext";
import { ThemeColors, useTheme } from "@/context/ThemeContext";

const ReadingsScreen: React.FC = () => {
   const { colors } = useTheme();
   const commonStyles = getCommonStyles(colors);
   const styles = getStyles(colors);

   const { accessToken } = useAuth();
   const router = useRouter();
   const [readings, setReadings] = useState<any[]>([]);

   useEffect(() => {
      const fetchData = async () => {
         if (accessToken) {
            const readings = await apiService.get<any[]>(
               "/readings?page=all", // TODO: Paginate this
               accessToken,
            );
            setReadings(readings);
         }
      };

      fetchData();
   }, []);

   return (
      <View>
         <ScrollView contentContainerStyle={styles.scrollContainer}>
            <View style={styles.container}>
               <Text style={styles.title}>Readings</Text>

               <View style={styles.readingsContainer}>
                  {readings.length > 0 ? (
                     readings.map((reading) => (
                        <TouchableOpacity
                           key={reading.id}
                           onPress={() =>
                              router.push(`/(app)/reading?id=${reading.id}`)
                           }
                        >
                           <ReadingCard
                              reading={reading.details.deviceReading}
                           />
                        </TouchableOpacity>
                     ))
                  ) : (
                     <Text>No readings found.</Text>
                  )}
               </View>
            </View>
         </ScrollView>
         <View style={styles.footer}>
            <Pressable
               style={({ pressed }) => [
                  commonStyles.button,
                  pressed && commonStyles.buttonPressed,
               ]}
               onPress={() => router.push("/(app)/reading")}
            >
               <Text style={commonStyles.buttonText}>New Reading</Text>
            </Pressable>
         </View>
      </View>
   );
};

const getStyles = (colors: ThemeColors) =>
   StyleSheet.create({
      safeArea: {
         flex: 1,
         backgroundColor: colors.background,
      },
      scrollContainer: {
         flexGrow: 1,
      },
      container: {
         flex: 1,
         backgroundColor: colors.background,
         paddingHorizontal: spacing.lg,
         paddingTop: spacing.md,
      },
      title: {
         fontSize: 28,
         fontWeight: "bold",
         color: colors.text,
         textAlign: "center",
         marginBottom: spacing.xl,
      },
      readingsContainer: {
         gap: spacing.md,
      },
      footer: {
         paddingHorizontal: spacing.lg,
         paddingVertical: spacing.md,
      },
   });

export default ReadingsScreen;
