import React from "react";
import { Stack } from "expo-router";
import { View } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";

import { useAuth } from "@/context/AuthContext";
import { useTheme } from "@/context/ThemeContext";
import { getCommonStyles } from "@/constants/styles";
import { ActivityIndicator } from "react-native-paper";

const AuthenticatedLayout: React.FC = () => {
   const { isAuthenticated, isLoading } = useAuth();
   const { colors } = useTheme();
   const commonStyles = getCommonStyles(colors);

   return (
      <SafeAreaView style={commonStyles.safeArea}>
         {isLoading ? (
            <View
               style={{
                  flex: 1,
                  justifyContent: "center",
                  alignItems: "center",
               }}
            >
               <ActivityIndicator size="large" />
            </View>
         ) : (
            <Stack>
               <Stack.Protected guard={isAuthenticated}>
                  <Stack.Screen name="(app)" options={{ headerShown: false }} />
                  <Stack.Screen
                     name="(settings)"
                     options={{ headerShown: false }}
                  />
               </Stack.Protected>
               <Stack.Screen name="(auth)" options={{ headerShown: false }} />
               <Stack.Screen name="+not-found" />
            </Stack>
         )}
      </SafeAreaView>
   );
};

export default AuthenticatedLayout;
