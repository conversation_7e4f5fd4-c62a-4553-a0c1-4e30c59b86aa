import React, { useState } from "react";
import { <PERSON>, useRouter } from "expo-router";
import {
   View,
   Text,
   TextInput,
   SafeAreaView,
   KeyboardAvoidingView,
   Platform,
   Pressable,
   ActivityIndicator,
   ScrollView,
   Image,
   TouchableOpacity,
   StyleSheet,
} from "react-native";
import MaterialIcons from "@expo/vector-icons/MaterialIcons";
import { Feather } from "@expo/vector-icons";

import { apiService } from "@/service/api";
import { SignUpRequest, BackendUser } from "@/types/auth";
import { useAuth } from "@/context/AuthContext";
import ToastMessage from "@/components/ui/ToastMessage";
import { getCommonStyles } from "@/constants/styles";
import AppModal from "@/components/ui/AppModal";
import { ThemeColors, useTheme } from "@/context/ThemeContext";

type Strength = "weak" | "medium" | "strong" | "";
type Gender = "MALE" | "FEMALE" | "OTHER";

type Errors = {
   email: string | null;
   password: string | null;
   confirmPassword: string | null;
   firstName: string | null;
   lastName: string | null;
   dob: string | null;
   gender: string | null;
};

const calculatePasswordStrength = (passwordVerification: string): Strength => {
   if (
      passwordVerification.length >= 8 &&
      /[A-Z]/.test(passwordVerification) &&
      /\d/.test(passwordVerification) &&
      /[!@#$%^&*]/.test(passwordVerification)
   ) {
      return "strong";
   } else if (
      passwordVerification.length >= 6 &&
      /\d/.test(passwordVerification) &&
      /[A-Za-z]/.test(passwordVerification)
   ) {
      return "medium";
   } else if (passwordVerification.length > 0) {
      return "weak";
   }
   return "";
};

const passwordRequirements = [
   {
      label: "At least 8 characters",
      test: (passwordVerification: string) => passwordVerification.length >= 8,
   },
   {
      label: "At least one uppercase letter",
      test: (passwordVerification: string) =>
         /[A-Z]/.test(passwordVerification),
   },
   {
      label: "At least one number",
      test: (passwordVerification: string) => /\d/.test(passwordVerification),
   },
   {
      label: "At least one special character (!@#$%^&*)",
      test: (passwordVerification: string) =>
         /[!@#$%^&*]/.test(passwordVerification),
   },
];

const genderOptions = [
   { label: "Male", value: "MALE" },
   { label: "Female", value: "FEMALE" },
   { label: "Other", value: "OTHER" },
];

const SignUpScreen: React.FC = () => {
   const { processSignupSuccess } = useAuth();
   const { theme, colors } = useTheme();
   const styles = getStyles(colors);
   const commonStyles = getCommonStyles(colors);
   const router = useRouter();

   const [form, setForm] = useState({
      email: "",
      password: "",
      confirmPassword: "",
      firstName: "",
      lastName: "",
      dob: "",
      gender: "",
   });

   const [errors, setErrors] = useState<Errors>({
      email: null,
      password: null,
      confirmPassword: null,
      firstName: null,
      lastName: null,
      dob: null,
      gender: null,
   });

   const [passwordStrength, setPasswordStrength] = useState<Strength>("");
   const [isLoading, setIsLoading] = useState(false);
   const [toastVisible, setToastVisible] = useState(false);
   const [toastMessage, setToastMessage] = useState("");
   const [toastType, setToastType] = useState<"success" | "error">("error");
   const [showPassword, setShowPassword] = useState(false);
   const [showConfirmPassword, setShowConfirmPassword] = useState(false);
   const [showGenderModal, setShowGenderModal] = useState(false);
   const [passwordFocused, setPasswordFocused] = useState(false);

   const onFormChanged = (name: string, value: string) => {
      setForm((prev) => ({ ...prev, [name]: value }));
      setErrors((prev) => ({ ...prev, [name]: null }));

      if (name === "password") {
         setPasswordStrength(calculatePasswordStrength(value));
      }
   };

   const validateSignUpForm = (): boolean => {
      let valid = true;
      const newErrors: Errors = {
         email: null,
         password: null,
         confirmPassword: null,
         firstName: null,
         lastName: null,
         dob: null,
         gender: null,
      };

      if (!form.firstName.trim()) {
         newErrors.firstName = "First name is required";
         valid = false;
      }
      if (!form.lastName.trim()) {
         newErrors.lastName = "Last name is required";
         valid = false;
      }
      if (!form.email.trim()) {
         newErrors.email = "Email is required";
         valid = false;
      } else if (
         !/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i.test(form.email.trim())
      ) {
         newErrors.email = "Invalid email address";
         valid = false;
      }
      if (!form.password.trim()) {
         newErrors.password = "Password is required";
         valid = false;
      } else if (form.password.length < 8) {
         newErrors.password = "Password must have at least 8 characters";
         valid = false;
      }
      if (!form.confirmPassword.trim()) {
         newErrors.confirmPassword = "Please confirm your password";
         valid = false;
      } else if (form.password !== form.confirmPassword) {
         newErrors.confirmPassword = "Passwords do not match";
         valid = false;
      }
      if (!form.dob.trim()) {
         newErrors.dob = "Date of birth is required";
         valid = false;
      } else if (!/^\d{4}-\d{2}-\d{2}$/.test(form.dob.trim())) {
         newErrors.dob = "Invalid date format (YYYY-MM-DD)";
         valid = false;
      }
      if (!form.gender.trim()) {
         newErrors.gender = "Gender is required";
         valid = false;
      }
      setErrors(newErrors);
      return valid;
   };

   const handleSignup = async () => {
      if (!validateSignUpForm()) return;
      setIsLoading(true);
      setToastVisible(false);
      try {
         const userData = await apiService.post<BackendUser, SignUpRequest>(
            "/users/registration",
            {
               firstName: form.firstName,
               lastName: form.lastName,
               email: form.email,
               password: form.password,
               dob: form.dob,
               gender: form.gender as Gender,
            },
         );
         processSignupSuccess(userData);

         setToastMessage("Account created successfully! Please log in.");
         setToastType("success");
         setToastVisible(true);

         setTimeout(() => {
            router.replace("/(auth)/login");
         }, 1000);
      } catch (err) {
         const msg = err instanceof Error ? err.message.toLowerCase() : "";
         if (msg.includes("already exists") || msg.includes("duplicate")) {
            setToastMessage("Account already exists");
         } else {
            setToastMessage("Signup failed. Please try again later.");
         }
         setToastType("error");
         setToastVisible(true);
      } finally {
         setIsLoading(false);
      }
   };

   const handleGenderSelect = (value: string) => {
      setForm((prev) => ({ ...prev, gender: value }));
      setShowGenderModal(false);
      setErrors((prev) => ({ ...prev, gender: null }));
   };

   const strengthColors = {
      weak: colors.error,
      medium: "#f0ad4e",
      strong: "#5cb85c",
   } as const;
   const strengthWidths = {
      weak: "33%",
      medium: "66%",
      strong: "100%",
   } as const;

   return (
      <KeyboardAvoidingView
         behavior={Platform.OS === "ios" ? "padding" : "height"}
         style={commonStyles.keyboardAvoidingView}
      >
         <ToastMessage
            visible={toastVisible}
            message={toastMessage}
            type={toastType}
            onHide={() => setToastVisible(false)}
         />
         <ScrollView
            contentContainerStyle={commonStyles.scrollViewContent}
            keyboardShouldPersistTaps="handled"
         >
            <View style={commonStyles.formContainer}>
               <View style={commonStyles.logoContainer}>
                  <Image
                     source={require("@/assets/images/adaptit_logo_dark.png")}
                     style={commonStyles.logo}
                  />
               </View>
               <Text style={commonStyles.title}>Create an account</Text>

               <FormInput
                  icon="email"
                  placeholder="Email"
                  value={form.email}
                  name="email"
                  error={errors.email}
                  keyboardType="email-address"
                  onChange={onFormChanged}
                  colorScheme={theme}
                  autoCapitalize="none"
                  autoComplete="email"
                  textContentType="emailAddress"
               />

               <FormInput
                  icon="person"
                  placeholder="First Name"
                  value={form.firstName}
                  name="firstName"
                  error={errors.firstName}
                  onChange={onFormChanged}
                  colorScheme={theme}
                  autoComplete="given-name"
               />

               <FormInput
                  icon="person"
                  placeholder="Last Name"
                  value={form.lastName}
                  name="lastName"
                  error={errors.lastName}
                  onChange={onFormChanged}
                  colorScheme={theme}
                  autoComplete="family-name"
               />

               <FormInput
                  icon="lock"
                  placeholder="Password"
                  value={form.password}
                  name="password"
                  error={errors.password}
                  onChange={onFormChanged}
                  colorScheme={theme}
                  autoComplete="new-password"
                  textContentType="newPassword"
                  secureTextEntry={!showPassword}
                  rightIcon={showPassword ? "visibility-off" : "visibility"}
                  onRightIconPress={() => setShowPassword(!showPassword)}
                  onFocus={() => setPasswordFocused(true)}
                  onBlur={() => setPasswordFocused(false)}
               />

               {passwordFocused && (
                  <View
                     style={{
                        marginTop: 2,
                        marginBottom: 4,
                        paddingHorizontal: 14,
                     }}
                  >
                     {passwordRequirements.map((req, i) => {
                        const passed = req.test(form.password);
                        return (
                           <View
                              key={i}
                              style={{
                                 flexDirection: "row",
                                 alignItems: "center",
                                 marginBottom: 2,
                              }}
                           >
                              <Feather
                                 name={passed ? "check-circle" : "circle"}
                                 size={16}
                                 color={passed ? "#5cb85c" : "#bbb"}
                                 style={{ marginRight: 6 }}
                              />
                              <Text
                                 style={{
                                    fontSize: 13,
                                    color: passed ? "#5cb85c" : "#888",
                                    textDecorationLine: passed
                                       ? "none"
                                       : "none",
                                 }}
                              >
                                 {req.label}
                              </Text>
                           </View>
                        );
                     })}
                  </View>
               )}

               {passwordStrength !== "" && (
                  <View style={styles.strengthWrapper}>
                     <View style={styles.strengthTrack}>
                        <View
                           style={[
                              styles.strengthFill,
                              {
                                 backgroundColor:
                                    strengthColors[passwordStrength],
                                 width: strengthWidths[passwordStrength],
                              },
                           ]}
                        />
                     </View>
                     <Text style={styles.strengthLabel}>
                        {passwordStrength.charAt(0).toUpperCase() +
                           passwordStrength.slice(1)}
                     </Text>
                  </View>
               )}

               <FormInput
                  icon="lock-outline"
                  placeholder="Confirm Password"
                  value={form.confirmPassword}
                  name="confirmPassword"
                  error={errors.confirmPassword}
                  onChange={onFormChanged}
                  colorScheme={theme}
                  autoComplete="new-password"
                  textContentType="newPassword"
                  secureTextEntry={!showConfirmPassword}
                  rightIcon={
                     showConfirmPassword ? "visibility-off" : "visibility"
                  }
                  onRightIconPress={() =>
                     setShowConfirmPassword(!showConfirmPassword)
                  }
               />

               <FormInput
                  icon="calendar-today"
                  placeholder="YYYY-MM-DD"
                  value={form.dob}
                  name="dob"
                  error={errors.dob}
                  onChange={onFormChanged}
                  colorScheme={theme}
                  keyboardType="numbers-and-punctuation"
                  autoCapitalize="none"
                  autoCorrect={false}
                  maxLength={10}
               />

               <View
                  style={[
                     commonStyles.inputGroup,
                     errors.gender && {
                        borderColor: colors.error,
                        borderWidth: 1.2,
                     },
                  ]}
               >
                  <MaterialIcons
                     name="wc"
                     size={20}
                     color={colors.icon}
                     style={commonStyles.inputIcon}
                  />
                  <Pressable
                     style={[commonStyles.input, { justifyContent: "center" }]}
                     onPress={() => setShowGenderModal(true)}
                  >
                     <Text
                        style={{
                           color: form.gender ? colors.text : colors.icon,
                           fontSize: 16,
                        }}
                     >
                        {form.gender
                           ? genderOptions.find(
                                (opt) => opt.value === form.gender,
                             )?.label
                           : "Select Gender"}
                     </Text>
                  </Pressable>
                  <Pressable
                     onPress={() => setShowGenderModal(true)}
                     style={{ marginLeft: 8 }}
                  >
                     <MaterialIcons
                        name="arrow-drop-down"
                        size={24}
                        color={colors.icon}
                     />
                  </Pressable>
               </View>
               {errors.gender && (
                  <Text style={commonStyles.errorText}>{errors.gender}</Text>
               )}

               <AppModal
                  visible={showGenderModal}
                  onRequestClose={() => setShowGenderModal(false)}
                  title="Select Gender"
                  colorScheme={theme}
               >
                  {genderOptions.map((opt) => (
                     <TouchableOpacity
                        key={opt.value}
                        style={{
                           flexDirection: "row",
                           alignItems: "center",
                           justifyContent: "space-between",
                           paddingVertical: 12,
                           paddingHorizontal: 10,
                           width: "100%",
                           borderRadius: 8,
                        }}
                        onPress={() => handleGenderSelect(opt.value)}
                     >
                        <Text
                           style={{
                              fontSize: 16,
                              color:
                                 form.gender === opt.value
                                    ? colors.tint
                                    : colors.text,
                              fontWeight:
                                 form.gender === opt.value ? "bold" : "normal",
                           }}
                        >
                           {opt.label}
                        </Text>
                        {form.gender === opt.value && (
                           <MaterialIcons
                              name="check"
                              size={20}
                              color={colors.tint}
                              style={{ marginLeft: 10 }}
                           />
                        )}
                     </TouchableOpacity>
                  ))}
               </AppModal>

               <Pressable
                  style={({ pressed }) => [
                     commonStyles.button,
                     pressed && commonStyles.buttonPressed,
                     isLoading && commonStyles.buttonDisabled,
                  ]}
                  onPress={handleSignup}
                  disabled={isLoading}
               >
                  {isLoading ? (
                     <ActivityIndicator color={colors.text} />
                  ) : (
                     <Text style={commonStyles.buttonText}>Create Account</Text>
                  )}
               </Pressable>

               <View style={commonStyles.footer}>
                  <Text style={commonStyles.footerText}>
                     Already have an account?{" "}
                  </Text>
                  <Link href="/(auth)/login" replace asChild>
                     <Pressable>
                        <Text style={commonStyles.linkText}>Log In</Text>
                     </Pressable>
                  </Link>
               </View>
            </View>
         </ScrollView>
      </KeyboardAvoidingView>
   );
};

const FormInput = ({
   icon,
   placeholder,
   value,
   name,
   error,
   onChange,
   secureTextEntry,
   keyboardType,
   autoCapitalize,
   autoComplete,
   textContentType,
   autoCorrect,
   maxLength,
   rightIcon,
   onRightIconPress,
   onFocus,
   onBlur,
}: {
   icon: string;
   placeholder: string;
   value: string;
   name: string;
   error?: string | null;
   onChange: (name: string, value: string) => void;
   colorScheme: "light" | "dark";
   secureTextEntry?: boolean;
   keyboardType?: any;
   autoCapitalize?: any;
   autoComplete?: any;
   textContentType?: any;
   autoCorrect?: boolean;
   maxLength?: number;
   rightIcon?: string;
   onRightIconPress?: () => void;
   onFocus?: () => void;
   onBlur?: () => void;
}) => {
   const { colors } = useTheme();
   const commonStyles = getCommonStyles(colors);

   return (
      <>
         <View
            style={[
               commonStyles.inputGroup,
               error && { borderColor: colors.error, borderWidth: 1.2 },
            ]}
         >
            <MaterialIcons
               name={icon as any}
               size={20}
               color={colors.icon}
               style={commonStyles.inputIcon}
            />
            <TextInput
               style={commonStyles.input}
               placeholder={placeholder}
               placeholderTextColor={colors.icon}
               value={value}
               onChangeText={(text) => onChange(name, text)}
               secureTextEntry={secureTextEntry}
               keyboardType={keyboardType}
               autoCapitalize={autoCapitalize}
               autoComplete={autoComplete}
               textContentType={textContentType}
               autoCorrect={autoCorrect}
               maxLength={maxLength}
               onFocus={onFocus}
               onBlur={onBlur}
            />
            {rightIcon && (
               <Pressable
                  onPress={onRightIconPress}
                  style={commonStyles.eyeButton}
               >
                  <MaterialIcons
                     name={rightIcon as any}
                     size={20}
                     color={colors.icon}
                  />
               </Pressable>
            )}
         </View>
         {error && <Text style={commonStyles.errorText}>{error}</Text>}
      </>
   );
};

const getStyles = (colors: ThemeColors) =>
   StyleSheet.create({
      strengthWrapper: {
         marginTop: 4,
         marginBottom: 10,
         paddingHorizontal: 12,
      },
      strengthTrack: {
         width: "100%",
         height: 6,
         borderRadius: 4,
         backgroundColor: "#ddd",
         overflow: "hidden",
      },
      strengthFill: {
         height: "100%",
         borderRadius: 4,
         width: "0%",
      },
      strengthLabel: {
         marginTop: 4,
         fontSize: 12,
         color: "#888",
      },
   });

export default SignUpScreen;
