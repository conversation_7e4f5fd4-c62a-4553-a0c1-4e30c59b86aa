import { useEffect } from "react";
import { useRouter } from "expo-router";
import { InteractionManager } from "react-native";

import { useAuth } from "@/context/AuthContext";

const AuthIndex = () => {
   const { isAuthenticated } = useAuth();
   const router = useRouter();

   useEffect(() => {
      const task = InteractionManager.runAfterInteractions(() => {
         console.log(`Authentication status: ${isAuthenticated}`);

         if (!isAuthenticated) router.replace("/(auth)/login");
         else router.replace("/(app)");
      });

      return () => task.cancel();
   }, [isAuthenticated]);
   return null;
};

export default AuthIndex;
