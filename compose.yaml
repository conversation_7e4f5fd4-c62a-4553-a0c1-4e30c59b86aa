name: px-um

volumes:
  mysql-data: {}
  aidbox_pg_data: {}

services:
  # Existing MySQL database for user management
  db:
    image: mysql:8.0.40
    ports:
      - "3307:3306"
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD} # from .env
      MYSQL_DATABASE: ${MYSQL_DATABASE} # from .env
    networks:
      - net
    volumes:
      - mysql-data:/var/lib/mysql

  # Aidbox PostgreSQL database for FHIR data
  aidbox_db:
    image: healthsamurai/aidboxdb:17
    ports:
      - "5432:5432"
    volumes:
      - aidbox_pg_data:/var/lib/postgresql/data:delegated
    environment:
      POSTGRES_USER: aidbox
      POSTGRES_PORT: '5432'
      POSTGRES_DB: aidbox
      POSTGRES_PASSWORD: SjvWC3fGnO
    networks:
      - net

  # Aidbox FHIR server
  aidbox:
    image: healthsamurai/aidboxone:edge
    pull_policy: always
    depends_on:
      - aidbox_db
    ports:
      - "8101:8080"
    environment:
      AIDBOX_TERMINOLOGY_SERVICE_BASE_URL: https://tx.health-samurai.io/fhir
      AIDBOX_FHIR_PACKAGES: hl7.fhir.r4.core#4.0.1
      AIDBOX_LICENSE: eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.OAiwHc8D2hVhPKrG3pGjEhVS2s9FfgXTVHQQZpW4p40QdN9KX0E9Hk4Xn6M_dyYWd3eaWH69bIHj1aXc4BEiw5lEusCSxxqYPqSbAWHmMkX32PVqNGuybjRvR4iB95SmwhEmdpKkneHA5_7Xi3-DCRbFFu4KmnFMT4ylxH_6daM
      AIDBOX_FHIR_SCHEMA_VALIDATION: true
      AIDBOX_CREATED_AT_URL: https://aidbox.app/ex/createdAt
      BOX_SETTINGS_MODE: read-write
      AIDBOX_CLIENT_SECRET: hZpB2JalNt
      AIDBOX_CORRECT_AIDBOX_FORMAT: true
      AIDBOX_ADMIN_PASSWORD: uWC2eGmOZq
      AIDBOX_COMPLIANCE: enabled
      AIDBOX_SECURITY_AUDIT__LOG_ENABLED: true
      BOX_SEARCH_FHIR__COMPARISONS: true
      PGHOST: aidbox_db
      BOX_COMPATIBILITY_VALIDATION_JSON__SCHEMA_REGEX: '#{:fhir-datetime}'
      BOX_SEARCH_AUTHORIZE_INLINE_REQUESTS: true
      PGUSER: aidbox
      AIDBOX_PORT: 8080
      PGDATABASE: aidbox
      PGPASSWORD: SjvWC3fGnO
      PGPORT: '5432'
      BOX_SEARCH_INCLUDE_CONFORMANT: true
    networks:
      - net

  # Your existing Spring Boot backend (User Management)
  backend:
    image: openjdk:21-jdk
    ports:
      - "8081:8080"
    networks:
      - net
    volumes:
      - ./backend:/spring-app
      - ./backend/local-file-storage:/spring-app/local-file-storage
    working_dir: /spring-app
    environment:
      SPRING_DATASOURCE_URL: jdbc:mysql://db:3306/${MYSQL_DATABASE} # from .env
      SPRING_DATASOURCE_USERNAME: root
      SPRING_DATASOURCE_PASSWORD: ${MYSQL_ROOT_PASSWORD} # from .env
      SPRING_JPA_HIBERNATE_DDL_AUTO: update
      JWT_PRIVATE_KEY_PATH: ${PRIVATE_KEY_PATH} # from .env
      JWT_PUBLIC_KEY_PATH: ${PUBLIC_KEY_PATH} # from .env
      FRONT_END_URL: ${FRONT_END_URL} # from .env
    command: bash -c "chmod +x ./mvnw && ./mvnw spring-boot:run && tail -f /dev/null"
    depends_on:
      - db
      - aidbox

  frontend:
    image: node:22.13.0-alpine3.21
    ports:
      - "5173:5173"
    networks:
      - net
    volumes:
      - ./frontend:/vue-app
    working_dir: /vue-app
    command: sh -c "npm install && npm run dev"
    depends_on:
      - backend
    environment:
      - VITE_BACKEND_URL=${BACKEND_URL}:8081

networks:
  net:
    driver: bridge
